import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://tkxbnghqafgzqlaaheuk.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRreGJuZ2hxYWZnenFsYWFoZXVrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTUyNTc0OTAsImV4cCI6MjA3MDgzMzQ5MH0.K5ptEhI0H155-b8e0dYGuuNeghInS8ZOqx7BLVcqitQ'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface Company {
  id: string
  name: string
  logo_url?: string
  website_url?: string
  description?: string
  location?: string
  size_range?: string
  industry?: string
  founded_year?: number
  is_verified: boolean
  created_at: string
  updated_at: string
}

export interface Job {
  id: string
  company_id: string
  title: string
  slug: string
  description: string
  requirements?: string
  location?: string
  location_type: string
  salary_min?: number
  salary_max?: number
  salary_currency: string
  employment_type: string
  experience_level?: string
  benefits?: string[]
  tags?: string[]
  is_featured: boolean
  is_active: boolean
  view_count: number
  application_count: number
  external_apply_url?: string
  how_to_apply?: string
  posted_by?: string
  posted_at: string
  expires_at?: string
  created_at: string
  updated_at: string
  company?: Company
  job_locations?: JobLocation
  featured_jobs?: FeaturedJob
}

export interface JobLocation {
  id: string
  job_id: string
  street_address?: string
  city?: string
  state_province?: string
  country: string
  postal_code?: string
  latitude?: number
  longitude?: number
  timezone?: string
  is_remote_friendly: boolean
  office_type: string
  created_at: string
  updated_at: string
}

export interface FeaturedJob {
  id: string
  job_id: string
  company_id: string
  featured_type: string
  priority_order: number
  featured_until: string
  created_by?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface CompanySubscription {
  id: string
  company_id: string
  plan_type: string
  status: string
  price_per_month?: number
  job_posting_limit: number
  featured_listing_limit: number
  analytics_access: boolean
  custom_branding: boolean
  priority_support: boolean
  started_at: string
  expires_at?: string
  created_at: string
  updated_at: string
}

export interface CompanyBranding {
  id: string
  company_id: string
  primary_color: string
  secondary_color: string
  accent_color: string
  custom_logo_url?: string
  banner_image_url?: string
  custom_css?: string
  brand_description?: string
  created_at: string
  updated_at: string
}

export interface JobAnalytics {
  id: string
  job_id: string
  date: string
  views_count: number
  applications_count: number
  saves_count: number
  unique_views_count: number
  click_through_rate: number
  created_at: string
  updated_at: string
}