import { supabase } from './supabase'

export interface JobApplication {
  id?: string
  job_id: string
  user_id: string
  status: string
  applied_at?: string
  notes?: string
  withdrawn_at?: string | null
  cover_letter?: string
  resume_url?: string
  contact_email?: string
}

// Apply to a job
export async function applyToJob(
  jobId: string,
  userId: string,
  notes?: string,
  coverLetter?: string,
  contactEmail?: string
): Promise<{ data: JobApplication | null; error: any }> {
  try {
    // Check if user already applied to this job
    const { data: existingApplication } = await supabase
      .from('job_applications')
      .select('*')
      .eq('job_id', jobId)
      .eq('user_id', userId)
      .maybeSingle()

    if (existingApplication) {
      return {
        data: null,
        error: { message: 'You have already applied to this job' }
      }
    }

    // Create new application
    const { data, error } = await supabase
      .from('job_applications')
      .insert([
        {
          job_id: jobId,
          user_id: userId,
          status: 'applied',
          applied_at: new Date().toISOString(),
          notes: notes || null,
          cover_letter: coverLetter || null,
          contact_email: contactEmail || null
        }
      ])
      .select()
      .single()

    return { data, error }
  } catch (error) {
    return { data: null, error }
  }
}

// Check if user has already applied to a job
export async function hasAppliedToJob(
  jobId: string,
  userId: string
): Promise<{ hasApplied: boolean; application: JobApplication | null }> {
  try {
    const { data, error } = await supabase
      .from('job_applications')
      .select('*')
      .eq('job_id', jobId)
      .eq('user_id', userId)
      .maybeSingle()

    if (error) {
      console.error('Error checking job application:', error)
      return { hasApplied: false, application: null }
    }

    return {
      hasApplied: !!data,
      application: data
    }
  } catch (error) {
    console.error('Error checking job application:', error)
    return { hasApplied: false, application: null }
  }
}

// Withdraw job application
export async function withdrawJobApplication(
  applicationId: string,
  userId: string
): Promise<{ data: JobApplication | null; error: any }> {
  try {
    const { data, error } = await supabase
      .from('job_applications')
      .update({
        status: 'withdrawn',
        withdrawn_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', applicationId)
      .eq('user_id', userId)
      .select()
      .single()

    return { data, error }
  } catch (error) {
    return { data: null, error }
  }
}

// Get user's job applications
export async function getUserJobApplications(
  userId: string
): Promise<{ data: any[] | null; error: any }> {
  try {
    const { data, error } = await supabase
      .from('job_applications')
      .select(`
        *,
        jobs!inner(
          id,
          title,
          slug,
          location_type,
          location,
          companies!inner(
            name,
            logo_url
          )
        )
      `)
      .eq('user_id', userId)
      .order('applied_at', { ascending: false })

    return { data, error }
  } catch (error) {
    return { data: null, error }
  }
}