import * as React from "react"
import { cn } from "../../lib/utils"

interface DropdownMenuContextType {
  open: boolean
  setOpen: (open: boolean) => void
}

const DropdownMenuContext = React.createContext<DropdownMenuContextType | undefined>(undefined)

const DropdownMenu = ({ children }: { children: React.ReactNode }) => {
  const [open, setOpen] = React.useState(false)
  
  return (
    <DropdownMenuContext.Provider value={{ open, setOpen }}>
      <div className="relative inline-block text-left">
        {children}
      </div>
    </DropdownMenuContext.Provider>
  )
}

const DropdownMenuTrigger = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement> & {
    asChild?: boolean
  }
>(({ className, children, asChild, ...props }, ref) => {
  const context = React.useContext(DropdownMenuContext)
  if (!context) throw new Error("DropdownMenuTrigger must be used within DropdownMenu")
  
  const { setOpen } = context
  
  if (asChild) {
    const child = children as React.ReactElement<any>
    return React.cloneElement(child, {
      // forward ref if possible
      ref,
      className: cn(child.props.className, className),
      onClick: (e: React.MouseEvent) => {
        child.props.onClick?.(e)
        ;(props as any).onClick?.(e)
        setOpen((s: boolean) => !s)
      },
      onDoubleClick: (e: React.MouseEvent) => {
        child.props.onDoubleClick?.(e)
        ;(props as any).onDoubleClick?.(e)
        setOpen(false)
      },
      ...props
    })
  }
  
  return (
    <button
      ref={ref}
      className={className}
      onClick={(e) => {
        (props as any).onClick?.(e)
        setOpen((s) => !s)
      }}
      onDoubleClick={(e) => {
        (props as any).onDoubleClick?.(e)
        setOpen(false)
      }}
      {...props}
    >
      {children}
    </button>
  )
})
DropdownMenuTrigger.displayName = "DropdownMenuTrigger"

const DropdownMenuContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    align?: "start" | "center" | "end"
    forceMount?: boolean
  }
>(({ className, children, align = "center", ...props }, ref) => {
  const context = React.useContext(DropdownMenuContext)
  if (!context) throw new Error("DropdownMenuContent must be used within DropdownMenu")
  
  const { open, setOpen } = context
  
  React.useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const node = (ref as React.RefObject<HTMLDivElement>)?.current
      if (node && !node.contains(e.target as Node)) {
        setOpen(false)
      }
    }
    if (open) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [open, setOpen, ref])
  
  if (!open) return null
  
  return (
    <div
      ref={ref}
      className={cn(
        "absolute z-50 min-w-32 overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md",
        "border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700",
        align === "end" && "right-0",
        align === "start" && "left-0",
        className
      )}
      onClick={(e) => e.stopPropagation()}
      {...props}
    >
      {children}
    </div>
  )
})
DropdownMenuContent.displayName = "DropdownMenuContent"

const DropdownMenuItem = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    asChild?: boolean
  }
>(({ className, children, asChild, ...props }, ref) => {
  const context = React.useContext(DropdownMenuContext)
  // If used inside DropdownMenu, close on click
  const setOpen = context?.setOpen
  
  if (asChild) {
    const child = children as React.ReactElement<any>
    return React.cloneElement(child, {
      ref,
      className: cn(
        "relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-disabled:pointer-events-none data-disabled:opacity-50",
        "hover:bg-gray-100 dark:hover:bg-gray-600",
        child.props.className,
        className
      ),
      onClick: (e: React.MouseEvent) => {
        child.props.onClick?.(e)
        setOpen?.(false)
      },
      ...props
    })
  }
  
  return (
    <div
      ref={ref}
      className={cn(
        "relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-disabled:pointer-events-none data-disabled:opacity-50",
        "hover:bg-gray-100 dark:hover:bg-gray-600",
        className
      )}
      onClick={(e) => {
        props.onClick?.(e as any)
        setOpen?.(false)
      }}
      {...props}
    >
      {children}
    </div>
  )
})
DropdownMenuItem.displayName = "DropdownMenuItem"

const DropdownMenuLabel = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("px-2 py-1.5 text-sm font-semibold text-gray-900 dark:text-white", className)}
    {...props}
  />
))
DropdownMenuLabel.displayName = "DropdownMenuLabel"

const DropdownMenuSeparator = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("-mx-1 my-1 h-px bg-muted bg-gray-200 dark:bg-gray-600", className)}
    {...props}
  />
))
DropdownMenuSeparator.displayName = "DropdownMenuSeparator"

export {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
}