import React from 'react'
import { motion } from 'framer-motion'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Link } from 'react-router-dom'
import { ExternalLink, MapPin, Users, Briefcase } from 'lucide-react'

const featuredCompanies = [
  {
    id: '1',
    name: 'TechGlobal Inc.',
    logo: '/images/modern_tech_company_office_workspace.jpg',
    industry: 'Technology',
    size: '1000-5000',
    location: 'San Francisco, CA',
    openJobs: 24,
    isVerified: true,
    description: 'Leading AI and machine learning company building the future of intelligent automation.'
  },
  {
    id: '2',
    name: 'RemoteFirst Co.',
    logo: '/images/modern_tech_company_conference_room_workspace.jpg',
    industry: 'Software',
    size: '250-1000',
    location: 'Remote First',
    openJobs: 18,
    isVerified: true,
    description: 'Pioneering remote collaboration tools used by millions worldwide.'
  },
  {
    id: '3',
    name: 'DataVision Labs',
    logo: '/images/modern_professional_tech_office_workspace.jpg',
    industry: 'Data Science',
    size: '100-250',
    location: 'London, UK',
    openJobs: 12,
    isVerified: true,
    description: 'Transforming businesses through advanced analytics and data visualization.'
  },
  {
    id: '4',
    name: 'CloudScale Systems',
    logo: '/images/modern_tech_office_professional_workspace_interior.jpg',
    industry: 'Cloud Computing',
    size: '500-1000',
    location: 'Toronto, Canada',
    openJobs: 31,
    isVerified: true,
    description: 'Scalable cloud infrastructure solutions for enterprise customers.'
  }
]

export default function FeaturedCompanies() {
  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-800/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge className="inline-flex items-center space-x-2 bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800 mb-6">
            <Briefcase className="w-4 h-4" />
            <span>Trusted by Industry Leaders</span>
          </Badge>
          
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Companies Building
            <span className="block text-transparent bg-clip-text bg-linear-to-r from-green-600 to-blue-600 dark:from-green-400 dark:to-blue-400">
              Remote-First Teams
            </span>
          </h2>
          
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Join innovative companies that are reshaping the future of work 
            with distributed teams and global talent.
          </p>
        </motion.div>

        {/* Companies Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {featuredCompanies.map((company, index) => (
            <motion.div
              key={company.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200 group"
            >
              <div className="flex items-start space-x-4">
                {/* Company Logo */}
                <div className="shrink-0">
                  <img
                    src={company.logo}
                    alt={`${company.name} office`}
                    className="w-16 h-16 rounded-xl object-cover border border-gray-200 dark:border-gray-700"
                  />
                </div>
                
                {/* Company Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                      {company.name}
                    </h3>
                    {company.isVerified && (
                      <Badge variant="secondary" className="text-xs bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300">
                        Verified
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400 mb-3">
                    <span className="flex items-center">
                      <Briefcase className="w-4 h-4 mr-1" />
                      {company.industry}
                    </span>
                    <span className="flex items-center">
                      <Users className="w-4 h-4 mr-1" />
                      {company.size}
                    </span>
                  </div>
                  
                  <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-3">
                    <MapPin className="w-4 h-4 mr-1" />
                    {company.location}
                  </div>
                  
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                    {company.description}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div className="text-sm">
                      <span className="font-semibold text-blue-600 dark:text-blue-400">
                        {company.openJobs}
                      </span>
                      <span className="text-gray-600 dark:text-gray-400 ml-1">
                        open positions
                      </span>
                    </div>
                    
                    <Link to={`/company/${company.id}`}>
                      <Button 
                        size="sm" 
                        variant="outline"
                        className="group-hover:bg-blue-600 group-hover:text-white group-hover:border-blue-600 transition-all duration-200"
                      >
                        View Jobs
                        <ExternalLink className="w-3 h-3 ml-1" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <Link to="/companies">
            <Button size="lg" className="bg-linear-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-200">
              Explore All Companies
              <ExternalLink className="ml-2 w-5 h-5" />
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  )
}