import React from 'react'
import { motion } from 'framer-motion'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { 
  TrendingUp, 
  Users, 
  MapPin, 
  Briefcase,
  Globe,
  Clock,
  Award,
  Zap
} from 'lucide-react'

export default function StatsSection() {
  const stats = [
    {
      icon: Briefcase,
      value: '2,500+',
      label: 'Active Remote Jobs',
      description: 'Across all industries',
      color: 'blue'
    },
    {
      icon: Users,
      value: '850+',
      label: 'Verified Companies',
      description: 'From startups to Fortune 500',
      color: 'green'
    },
    {
      icon: MapPin,
      value: '180+',
      label: 'Countries Supported',
      description: 'Truly global opportunities',
      color: 'purple'
    },
    {
      icon: TrendingUp,
      value: '95%',
      label: 'Success Rate',
      description: 'Job placements within 60 days',
      color: 'orange'
    }
  ]

  const features = [
    {
      icon: Globe,
      title: 'Global Network',
      description: 'Connect with companies worldwide'
    },
    {
      icon: Clock,
      title: 'Real-time Updates',
      description: 'Get notified of new opportunities instantly'
    },
    {
      icon: Award,
      title: 'Quality Assured',
      description: 'All companies are verified and vetted'
    },
    {
      icon: Zap,
      title: 'Fast Applications',
      description: 'Apply to multiple jobs with one click'
    }
  ]

  return (
    <section className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge className="inline-flex items-center space-x-2 bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800 mb-6">
            <TrendingUp className="w-4 h-4" />
            <span>Platform Statistics</span>
          </Badge>
          
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Trusted by Companies
            <span className="block text-transparent bg-clip-text bg-linear-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400">
              & Professionals Worldwide
            </span>
          </h2>
          
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Join thousands of remote professionals and hundreds of companies 
            building the future of work, together.
          </p>
        </motion.div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {stats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center group"
              >
                <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl mb-4 transition-all duration-200 group-hover:scale-110 ${
                  stat.color === 'blue' ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400' :
                  stat.color === 'green' ? 'bg-green-100 dark:bg-green-900/50 text-green-600 dark:text-green-400' :
                  stat.color === 'purple' ? 'bg-purple-100 dark:bg-purple-900/50 text-purple-600 dark:text-purple-400' :
                  'bg-orange-100 dark:bg-orange-900/50 text-orange-600 dark:text-orange-400'
                }`}>
                  <Icon className="w-8 h-8" />
                </div>
                
                <div className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-2">
                  {stat.value}
                </div>
                
                <div className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-1">
                  {stat.label}
                </div>
                
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {stat.description}
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center p-6 rounded-2xl bg-gray-50 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800 border border-gray-200 dark:border-gray-700 transition-all duration-200 hover:shadow-lg"
              >
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-xl bg-linear-to-br from-blue-500 to-blue-600 text-white mb-4">
                  <Icon className="w-6 h-6" />
                </div>
                
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {feature.title}
                </h3>
                
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  {feature.description}
                </p>
              </motion.div>
            )
          })}
        </div>
      </div>
    </section>
  )
}