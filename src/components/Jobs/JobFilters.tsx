import React from 'react'
import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Filter,
  X,
  MapPin,
  Briefcase,
  DollarSign,
  Clock,
  Star,
  Tag,
  Building2
} from 'lucide-react'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Checkbox } from '../ui/checkbox'
import { Slider } from '../ui/slider'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Separator } from '../ui/separator'

interface JobFiltersProps {
  filters: {
    search: string
    location: string
    employment_type: string[]
    experience_level: string[]
    salary_min: number
    is_featured: boolean
    tags: string[]
  }
  onFiltersChange: (filters: any) => void
  showMobile: boolean
  onToggleMobile: () => void
}

const employmentTypes = [
  'Full-time',
  'Part-time',
  'Contract',
  'Freelance',
  'Internship'
]

const experienceLevels = [
  'Entry Level',
  'Mid Level',
  'Senior Level',
  'Lead/Principal',
  'Executive'
]

const popularTags = [
  'JavaScript',
  'Python',
  'React',
  'Node.js',
  'TypeScript',
  'AWS',
  'DevOps',
  'Machine Learning',
  'Frontend',
  'Backend',
  'Full Stack',
  'Mobile',
  'UI/UX',
  'Data Science',
  'Product Management'
]

const locations = [
  'United States',
  'United Kingdom',
  'Canada',
  'Germany',
  'Australia',
  'Netherlands',
  'France',
  'Spain',
  'Remote (Global)',
  'Remote (US Only)',
  'Remote (EU Only)'
]

export default function JobFilters({ filters, onFiltersChange, showMobile, onToggleMobile }: JobFiltersProps) {
  const [activeFilters, setActiveFilters] = useState(0)

  // Count active filters
  React.useEffect(() => {
    let count = 0
    if (filters.employment_type.length > 0) count++
    if (filters.experience_level.length > 0) count++
    if (filters.salary_min > 0) count++
    if (filters.is_featured) count++
    if (filters.tags.length > 0) count++
    setActiveFilters(count)
  }, [filters])

  const handleEmploymentTypeChange = (type: string, checked: boolean) => {
    const updated = checked 
      ? [...filters.employment_type, type]
      : filters.employment_type.filter(t => t !== type)
    onFiltersChange({ employment_type: updated })
  }

  const handleExperienceLevelChange = (level: string, checked: boolean) => {
    const updated = checked 
      ? [...filters.experience_level, level]
      : filters.experience_level.filter(l => l !== level)
    onFiltersChange({ experience_level: updated })
  }

  const handleTagChange = (tag: string, checked: boolean) => {
    const updated = checked 
      ? [...filters.tags, tag]
      : filters.tags.filter(t => t !== tag)
    onFiltersChange({ tags: updated })
  }

  const clearAllFilters = () => {
    onFiltersChange({
      employment_type: [],
      experience_level: [],
      salary_min: 0,
      is_featured: false,
      tags: []
    })
  }

  const FilterContent = () => (
    <div className="space-y-6">
      {/* Filter Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Filter className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Filters
          </h3>
          {activeFilters > 0 && (
            <Badge variant="secondary" className="ml-2">
              {activeFilters}
            </Badge>
          )}
        </div>
        {activeFilters > 0 && (
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={clearAllFilters}
            className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
          >
            Clear All
          </Button>
        )}
      </div>

      <Separator />

      {/* Featured Jobs */}
      <div>
        <div className="flex items-center space-x-2 mb-3">
          <Star className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          <h4 className="font-medium text-gray-900 dark:text-white">
            Job Type
          </h4>
        </div>
        <div className="flex items-center space-x-2">
          <Checkbox
            id="featured"
            checked={filters.is_featured}
            onCheckedChange={(checked) => onFiltersChange({ is_featured: checked })}
          />
          <label htmlFor="featured" className="text-sm text-gray-600 dark:text-gray-400 cursor-pointer">
            Featured Jobs Only
          </label>
        </div>
      </div>

      <Separator />

      {/* Employment Type */}
      <div>
        <div className="flex items-center space-x-2 mb-3">
          <Briefcase className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          <h4 className="font-medium text-gray-900 dark:text-white">
            Employment Type
          </h4>
        </div>
        <div className="space-y-2">
          {employmentTypes.map((type) => (
            <div key={type} className="flex items-center space-x-2">
              <Checkbox
                id={type}
                checked={filters.employment_type.includes(type)}
                onCheckedChange={(checked) => handleEmploymentTypeChange(type, checked as boolean)}
              />
              <label htmlFor={type} className="text-sm text-gray-600 dark:text-gray-400 cursor-pointer">
                {type}
              </label>
            </div>
          ))}
        </div>
      </div>

      <Separator />

      {/* Experience Level */}
      <div>
        <div className="flex items-center space-x-2 mb-3">
          <Building2 className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          <h4 className="font-medium text-gray-900 dark:text-white">
            Experience Level
          </h4>
        </div>
        <div className="space-y-2">
          {experienceLevels.map((level) => (
            <div key={level} className="flex items-center space-x-2">
              <Checkbox
                id={level}
                checked={filters.experience_level.includes(level)}
                onCheckedChange={(checked) => handleExperienceLevelChange(level, checked as boolean)}
              />
              <label htmlFor={level} className="text-sm text-gray-600 dark:text-gray-400 cursor-pointer">
                {level}
              </label>
            </div>
          ))}
        </div>
      </div>

      <Separator />

      {/* Salary Range */}
      <div>
        <div className="flex items-center space-x-2 mb-3">
          <DollarSign className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          <h4 className="font-medium text-gray-900 dark:text-white">
            Minimum Salary
          </h4>
        </div>
        <div className="space-y-3">
          <div className="text-center">
            <span className="text-lg font-semibold text-blue-600 dark:text-blue-400">
              ${filters.salary_min.toLocaleString()}k+
            </span>
          </div>
          <Slider
            value={[filters.salary_min]}
            onValueChange={(value) => onFiltersChange({ salary_min: value[0] })}
            max={200}
            step={10}
            className="w-full"
          />
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>$0k</span>
            <span>$200k+</span>
          </div>
        </div>
      </div>

      <Separator />

      {/* Technology Tags */}
      <div>
        <div className="flex items-center space-x-2 mb-3">
          <Tag className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          <h4 className="font-medium text-gray-900 dark:text-white">
            Technologies & Skills
          </h4>
        </div>
        <div className="flex flex-wrap gap-2">
          {popularTags.map((tag) => (
            <Button
              key={tag}
              variant={filters.tags.includes(tag) ? "default" : "outline"}
              size="sm"
              onClick={() => handleTagChange(tag, !filters.tags.includes(tag))}
              className={`text-xs h-8 ${
                filters.tags.includes(tag) 
                  ? 'bg-blue-600 text-white border-blue-600' 
                  : 'hover:bg-blue-50 dark:hover:bg-blue-950/50 hover:border-blue-300 dark:hover:border-blue-700'
              }`}
            >
              {tag}
            </Button>
          ))}
        </div>
      </div>
    </div>
  )

  return (
    <>
      {/* Desktop Filters */}
      <div className="hidden lg:block">
        <Card className="sticky top-24">
          <CardContent className="p-6">
            <FilterContent />
          </CardContent>
        </Card>
      </div>

      {/* Mobile Filters */}
      {showMobile && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 lg:hidden"
        >
          <div className="fixed inset-0 bg-black/50" onClick={onToggleMobile} />
          <motion.div
            initial={{ x: '-100%' }}
            animate={{ x: 0 }}
            exit={{ x: '-100%' }}
            transition={{ type: 'spring', damping: 20 }}
            className="fixed left-0 top-0 h-full w-80 bg-white dark:bg-gray-900 shadow-xl overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Filters
                </h3>
                <Button variant="ghost" size="sm" onClick={onToggleMobile}>
                  <X className="w-5 h-5" />
                </Button>
              </div>
              <FilterContent />
            </div>
          </motion.div>
        </motion.div>
      )}
    </>
  )
}