import React from 'react'
import { X, MapPin, DollarSign, Clock, Eye, Users, ExternalLink, Building, Calendar } from 'lucide-react'
import { Job } from '../../lib/supabase'
import { useJobStats } from '../../hooks/useJobs'
import { useAuth } from '../../contexts/AuthContext'

interface JobModalProps {
  job: Job
  isOpen: boolean
  onClose: () => void
}

export default function JobModal({ job, isOpen, onClose }: JobModalProps) {
  const { user } = useAuth()
  const jobStats = useJobStats()

  if (!isOpen) return null

  const formatSalary = () => {
    if (!job.salary_min && !job.salary_max) return null
    
    const formatNumber = (num: number) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: job.salary_currency || 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(num)
    }

    if (job.salary_min && job.salary_max) {
      return `${formatNumber(job.salary_min)} - ${formatNumber(job.salary_max)}`
    } else if (job.salary_min) {
      return `From ${formatNumber(job.salary_min)}`
    } else if (job.salary_max) {
      return `Up to ${formatNumber(job.salary_max)}`
    }
    return null
  }

  const handleApplyClick = () => {
    // Record application attempt
    jobStats.mutate({
      action: 'apply',
      jobId: job.id,
      userId: user?.id
    })
    
    // Open external URL
    if (job.external_apply_url) {
      window.open(job.external_apply_url, '_blank')
    }
  }

  const timeAgo = () => {
    const now = new Date()
    const posted = new Date(job.posted_at)
    const diffInHours = Math.floor((now.getTime() - posted.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 24) {
      return `${diffInHours} hours ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays} days ago`
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-start justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-start space-x-4 flex-1">
            {/* Company Logo */}
            {job.company?.logo_url && (
              <img 
                src={job.company.logo_url} 
                alt={job.company.name}
                className="w-16 h-16 rounded-lg object-cover shrink-0"
                onError={(e) => {
                  (e.target as HTMLImageElement).style.display = 'none'
                }}
              />
            )}
            
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {job.title}
                </h1>
                {job.is_featured && (
                  <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 text-sm font-medium rounded-full">
                    Featured
                  </span>
                )}
              </div>
              
              <div className="flex items-center space-x-4 text-gray-600 dark:text-gray-400 mb-3">
                <div className="flex items-center space-x-1">
                  <Building className="w-4 h-4" />
                  <span className="font-medium text-gray-900 dark:text-white">{job.company?.name}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <MapPin className="w-4 h-4" />
                  <span>{job.location}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Calendar className="w-4 h-4" />
                  <span>Posted {timeAgo()}</span>
                </div>
              </div>

              {formatSalary() && (
                <div className="flex items-center space-x-1 text-green-600 dark:text-green-400 font-semibold text-lg">
                  <DollarSign className="w-5 h-5" />
                  <span>{formatSalary()}</span>
                </div>
              )}
            </div>
          </div>

          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 ml-4"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Job Description */}
              <section>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Job Description
                </h2>
                <div className="prose dark:prose-invert max-w-none">
                  <p className="text-gray-700 dark:text-gray-300 whitespace-pre-line">
                    {job.description}
                  </p>
                </div>
              </section>

              {/* Requirements */}
              {job.requirements && (
                <section>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    Requirements
                  </h2>
                  <div className="prose dark:prose-invert max-w-none">
                    <p className="text-gray-700 dark:text-gray-300 whitespace-pre-line">
                      {job.requirements}
                    </p>
                  </div>
                </section>
              )}

              {/* How to Apply */}
              {job.how_to_apply && (
                <section>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    How to Apply
                  </h2>
                  <div className="prose dark:prose-invert max-w-none">
                    <p className="text-gray-700 dark:text-gray-300 whitespace-pre-line">
                      {job.how_to_apply}
                    </p>
                  </div>
                </section>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Apply Button */}
              {job.external_apply_url && (
                <div className="sticky top-0">
                  <button
                    onClick={handleApplyClick}
                    className="w-full bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors flex items-center justify-center space-x-2"
                  >
                    <span>Apply for this Job</span>
                    <ExternalLink className="w-5 h-5" />
                  </button>
                </div>
              )}

              {/* Job Details */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-4">Job Details</h3>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Employment Type:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{job.employment_type}</span>
                  </div>
                  {job.experience_level && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Experience Level:</span>
                      <span className="font-medium text-gray-900 dark:text-white">{job.experience_level}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Location Type:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{job.location_type}</span>
                  </div>
                </div>
              </div>

              {/* Stats */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-4">Job Stats</h3>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Eye className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600 dark:text-gray-400">Views</span>
                    </div>
                    <span className="font-medium text-gray-900 dark:text-white">{job.view_count}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Users className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600 dark:text-gray-400">Applications</span>
                    </div>
                    <span className="font-medium text-gray-900 dark:text-white">{job.application_count}</span>
                  </div>
                </div>
              </div>

              {/* Skills/Tags */}
              {job.tags && job.tags.length > 0 && (
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-3">Skills Required</h3>
                  <div className="flex flex-wrap gap-2">
                    {job.tags.map((tag, index) => (
                      <span 
                        key={index}
                        className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Benefits */}
              {job.benefits && job.benefits.length > 0 && (
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-3">Benefits & Perks</h3>
                  <div className="space-y-2">
                    {job.benefits.map((benefit, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full shrink-0"></div>
                        <span className="text-sm text-gray-700 dark:text-gray-300">{benefit}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Company Info */}
              {job.company && (
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-3">About the Company</h3>
                  <div className="flex items-center space-x-3 mb-3">
                    {job.company.logo_url && (
                      <img 
                        src={job.company.logo_url} 
                        alt={job.company.name}
                        className="w-10 h-10 rounded object-cover"
                      />
                    )}
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">{job.company.name}</h4>
                      {job.company.website_url && (
                        <a 
                          href={job.company.website_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-green-600 dark:text-green-400 hover:underline flex items-center space-x-1"
                        >
                          <span>Visit Website</span>
                          <ExternalLink className="w-3 h-3" />
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}