import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { supabase, Job } from '../../lib/supabase'
import JobCard from './JobCard'
import { Loader2 } from 'lucide-react'

export default function JobsList() {
  const { data: jobs, isLoading, error } = useQuery({
    queryKey: ['jobs'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('jobs')
        .select(`
          *,
          company:companies(*),
          job_locations(*),
          featured_jobs(*)
        `)
        .eq('is_active', true)
        .order('posted_at', { ascending: false })
        .limit(20)
      
      if (error) throw error
      return data as Job[]
    },
  })

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600 dark:text-gray-400">Loading jobs...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 dark:text-red-400">Error loading jobs. Please try again.</p>
      </div>
    )
  }

  if (!jobs || jobs.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          No jobs found
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Check back later for new opportunities.
        </p>
      </div>
    )
  }

  // Separate featured and regular jobs
  const featuredJobs = jobs.filter(job => job.is_featured || job.featured_jobs)
  const regularJobs = jobs.filter(job => !job.is_featured && !job.featured_jobs)

  return (
    <div className="space-y-8">
      {/* Featured Jobs */}
      {featuredJobs.length > 0 && (
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            Featured Jobs
          </h2>
          <div className="grid gap-6">
            {featuredJobs.map((job) => (
              <JobCard key={job.id} job={job} featured />
            ))}
          </div>
        </div>
      )}

      {/* Regular Jobs */}
      {regularJobs.length > 0 && (
        <div>
          {featuredJobs.length > 0 && (
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              All Jobs
            </h2>
          )}
          <div className="grid gap-4">
            {regularJobs.map((job) => (
              <JobCard key={job.id} job={job} />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}