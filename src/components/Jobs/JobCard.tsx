import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { 
  MapPin,
  Clock,
  DollarSign,
  Building,
  Star,
  ExternalLink,
  Bookmark,
  Eye,
  Users,
  Zap,
  Crown
} from 'lucide-react'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Card, CardContent } from '../ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar'
import { Job } from '../../lib/supabase'
import { formatDistanceToNow } from 'date-fns'
import ApplyButton from './ApplyButton'

interface JobCardProps {
  job: Job
  featured?: boolean
}

export default function JobCard({ job, featured = false }: JobCardProps) {
  const formatSalary = () => {
    if (!job.salary_min && !job.salary_max) return null
    
    const currency = job.salary_currency === 'USD' ? '$' : job.salary_currency
    
    if (job.salary_min && job.salary_max) {
      return `${currency}${job.salary_min.toLocaleString()}k - ${currency}${job.salary_max.toLocaleString()}k`
    } else if (job.salary_min) {
      return `${currency}${job.salary_min.toLocaleString()}k+`
    } else if (job.salary_max) {
      return `Up to ${currency}${job.salary_max.toLocaleString()}k`
    }
    return null
  }

  const timeAgo = formatDistanceToNow(new Date(job.posted_at), { addSuffix: true })
  const salary = formatSalary()

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`group ${
        featured 
          ? 'relative overflow-hidden'
          : ''
      }`}
    >
      <Card className={`transition-all duration-200 hover:shadow-lg border ${
        featured 
          ? 'border-blue-200 dark:border-blue-800 bg-linear-to-br from-blue-50/50 to-indigo-50/50 dark:from-blue-950/20 dark:to-indigo-950/20 shadow-md'
          : 'border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600'
      }`}>
        {/* Featured Badge */}
        {featured && (
          <div className="absolute top-0 right-0">
            <div className="bg-linear-to-r from-blue-600 to-indigo-600 text-white px-3 py-1 text-xs font-medium rounded-bl-lg flex items-center space-x-1">
              <Crown className="w-3 h-3" />
              <span>Featured</span>
            </div>
          </div>
        )}

        <CardContent className={`p-6 ${featured ? 'pt-8' : ''}`}>
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-4 flex-1">
              {/* Company Logo */}
              <div className="shrink-0">
                <Avatar className={`${featured ? 'w-14 h-14' : 'w-12 h-12'} border-2 border-white dark:border-gray-800 shadow-sm`}>
                  <AvatarImage 
                    src={job.company?.logo_url || '/images/modern_tech_company_office_workspace.jpg'} 
                    alt={job.company?.name} 
                  />
                  <AvatarFallback className="bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 font-semibold">
                    {job.company?.name?.charAt(0) || 'C'}
                  </AvatarFallback>
                </Avatar>
              </div>

              {/* Job Details */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <Link 
                      to={`/jobs/${job.slug}`}
                      className="block group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200"
                    >
                      <h3 className={`font-semibold text-gray-900 dark:text-white line-clamp-1 ${
                        featured ? 'text-xl' : 'text-lg'
                      }`}>
                        {job.title}
                      </h3>
                    </Link>
                    
                    <Link 
                      to={`/company/${job.company_id}`}
                      className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 font-medium"
                    >
                      {job.company?.name}
                    </Link>
                  </div>
                  
                  {/* Action Buttons */}
                  <div className="flex items-center space-x-2 ml-4">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                    >
                      <Bookmark className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Job Meta */}
                <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-3">
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 mr-1" />
                    <span>{job.location || 'Remote'}</span>
                  </div>
                  
                  <div className="flex items-center">
                    <Building className="w-4 h-4 mr-1" />
                    <span>{job.employment_type}</span>
                  </div>
                  
                  {job.experience_level && (
                    <div className="flex items-center">
                      <Users className="w-4 h-4 mr-1" />
                      <span>{job.experience_level}</span>
                    </div>
                  )}
                  
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    <span>{timeAgo}</span>
                  </div>
                </div>

                {/* Salary */}
                {salary && (
                  <div className="flex items-center mb-3">
                    <DollarSign className="w-4 h-4 mr-1 text-green-600 dark:text-green-400" />
                    <span className="font-semibold text-green-600 dark:text-green-400">
                      {salary}
                    </span>
                  </div>
                )}

                {/* Description Preview */}
                <p className="text-gray-600 dark:text-gray-400 text-sm line-clamp-2 mb-4">
                  {job.description.replace(/<[^>]*>/g, '').substring(0, 150)}...
                </p>

                {/* Tags */}
                {job.tags && job.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mb-4">
                    {job.tags.slice(0, featured ? 6 : 4).map((tag) => (
                      <Badge 
                        key={tag} 
                        variant="secondary" 
                        className="text-xs bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-blue-100 dark:hover:bg-blue-900/50 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200"
                      >
                        {tag}
                      </Badge>
                    ))}
                    {job.tags.length > (featured ? 6 : 4) && (
                      <Badge variant="outline" className="text-xs">
                        +{job.tags.length - (featured ? 6 : 4)} more
                      </Badge>
                    )}
                  </div>
                )}

                {/* Bottom Row */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                    <div className="flex items-center">
                      <Eye className="w-3 h-3 mr-1" />
                      <span>{job.view_count} views</span>
                    </div>
                    
                    {job.application_count > 0 && (
                      <div className="flex items-center">
                        <Users className="w-3 h-3 mr-1" />
                        <span>{job.application_count} applied</span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    {featured && (
                      <Badge className="bg-linear-to-r from-yellow-400 to-orange-500 text-white border-0 text-xs">
                        <Zap className="w-3 h-3 mr-1" />
                        Hot Job
                      </Badge>
                    )}
                    
                    <ApplyButton
                      jobId={job.id}
                      jobTitle={job.title}
                      externalUrl={job.external_apply_url}
                      size="sm"
                      className={featured 
                        ? 'bg-linear-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-md hover:shadow-lg transform hover:scale-105'
                        : ''
                      }
                    />
                    
                    <Link to={`/jobs/${job.slug}`}>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="transition-all duration-200"
                      >
                        View Job
                        <ExternalLink className="w-3 h-3 ml-1" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}