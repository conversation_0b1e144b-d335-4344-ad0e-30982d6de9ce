import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '../ui/button'
import { useAuth } from '../../contexts/AuthContext'
import { applyToJob, hasAppliedToJob, withdrawJobApplication } from '../../lib/jobApplications'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '../ui/alert-dialog'
import { Textarea } from '../ui/textarea'
import { Input } from '../ui/input'
import { CheckCircle, ExternalLink, Trash2, Send } from 'lucide-react'
import { Link } from 'react-router-dom'

interface ApplyButtonProps {
  jobId: string
  jobTitle: string
  externalUrl?: string
  onApplicationSubmitted?: () => void
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive'
  size?: 'default' | 'sm' | 'lg'
  className?: string
}

export default function ApplyButton({
  jobId,
  jobTitle,
  externalUrl,
  onApplicationSubmitted,
  variant = 'default',
  size = 'default',
  className = ''
}: ApplyButtonProps) {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [hasApplied, setHasApplied] = useState(false)
  const [application, setApplication] = useState(null)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [notes, setNotes] = useState('')
  const [coverLetter, setCoverLetter] = useState('')
  const [contactEmail, setContactEmail] = useState('')

  // Check application status when component mounts or user changes
  useEffect(() => {
    if (user) {
      checkApplicationStatus()
      setContactEmail(user.email || '')
    }
  }, [user, jobId])

  const checkApplicationStatus = async () => {
    if (!user) return

    try {
      const { hasApplied: applied, application: app } = await hasAppliedToJob(jobId, user.id)
      setHasApplied(applied)
      setApplication(app)
    } catch (error) {
      console.error('Error checking application status:', error)
    }
  }

  const handleApply = async () => {
    if (!user) return

    setLoading(true)
    try {
      const { data, error } = await applyToJob(
        jobId,
        user.id,
        notes.trim() || undefined,
        coverLetter.trim() || undefined,
        contactEmail.trim() || user.email
      )

      if (error) {
        alert(error.message || 'Failed to submit application')
        return
      }

      setHasApplied(true)
      setApplication(data)
      setDialogOpen(false)
      setNotes('')
      setCoverLetter('')
      onApplicationSubmitted?.()
    } catch (error) {
      console.error('Error submitting application:', error)
      alert('Failed to submit application. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleWithdraw = async () => {
    if (!user || !application) return

    setLoading(true)
    try {
      const { error } = await withdrawJobApplication(application.id, user.id)

      if (error) {
        alert('Failed to withdraw application. Please try again.')
        return
      }

      setHasApplied(false)
      setApplication(null)
      onApplicationSubmitted?.()
    } catch (error) {
      console.error('Error withdrawing application:', error)
      alert('Failed to withdraw application. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  // If user is not logged in
  if (!user) {
    return (
      <Link to="/auth/login">
        <Button variant={variant} size={size} className={className}>
          <Send className="w-4 h-4 mr-2" />
          Sign in to Apply
        </Button>
      </Link>
    )
  }

  // If there's an external application URL
  if (externalUrl) {
    return (
      <a
        href={externalUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="inline-block"
      >
        <Button variant={variant} size={size} className={className}>
          <ExternalLink className="w-4 h-4 mr-2" />
          Apply Externally
        </Button>
      </a>
    )
  }

  // If user has already applied
  if (hasApplied && application) {
    if (application.status === 'withdrawn') {
      return (
        <div className="flex items-center space-x-2">
          <Button variant="outline" disabled size={size} className={className}>
            Application Withdrawn
          </Button>
        </div>
      )
    }

    return (
      <div className="flex items-center space-x-2">
        <Button variant="outline" disabled size={size} className={`${className} text-green-600 border-green-600`}>
          <CheckCircle className="w-4 h-4 mr-2" />
          Applied
        </Button>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
              <Trash2 className="w-4 h-4 mr-1" />
              Withdraw
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Withdraw Application</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to withdraw your application for {jobTitle}? 
                This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleWithdraw}
                disabled={loading}
                className="bg-red-600 hover:bg-red-700"
              >
                {loading ? 'Withdrawing...' : 'Withdraw Application'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    )
  }

  // Apply button with dialog
  return (
    <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
      <DialogTrigger asChild>
        <Button variant={variant} size={size} className={className}>
          <Send className="w-4 h-4 mr-2" />
          Apply Now
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Apply for {jobTitle}</DialogTitle>
          <DialogDescription>
            Fill out the form below to submit your application. All fields are optional.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div>
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
              Contact Email
            </label>
            <Input
              value={contactEmail}
              onChange={(e) => setContactEmail(e.target.value)}
              placeholder="<EMAIL>"
              type="email"
            />
            <p className="text-xs text-gray-500 mt-1">
              This will be used for employer communications
            </p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
              Cover Letter (Optional)
            </label>
            <Textarea
              value={coverLetter}
              onChange={(e) => setCoverLetter(e.target.value)}
              placeholder="Tell the employer why you're interested in this position..."
              rows={6}
            />
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
              Additional Notes (Optional)
            </label>
            <Textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Any additional information you'd like to include..."
              rows={3}
            />
          </div>
        </div>
        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={() => setDialogOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleApply} disabled={loading}>
            {loading ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
            ) : (
              <Send className="w-4 h-4 mr-2" />
            )}
            {loading ? 'Submitting...' : 'Submit Application'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}