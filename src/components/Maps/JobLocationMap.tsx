import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet'
import { Icon } from 'leaflet'
import 'leaflet/dist/leaflet.css'
import { MapPin, Building, Globe } from 'lucide-react'
import { JobLocation } from '../../lib/supabase'

// Fix for default markers in react-leaflet
const defaultIcon = new Icon({
  iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
  shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
})

interface JobLocationMapProps {
  location: JobLocation
  height?: string
}

export default function JobLocationMap({ location, height = '300px' }: JobLocationMapProps) {
  // Default to a central location if no coordinates provided
  const defaultLat = 40.7128
  const defaultLng = -74.0060
  
  const lat = location.latitude || defaultLat
  const lng = location.longitude || defaultLng
  
  const position: [number, number] = [lat, lng]

  const formatAddress = () => {
    const parts = []
    if (location.street_address) parts.push(location.street_address)
    if (location.city) parts.push(location.city)
    if (location.state_province) parts.push(location.state_province)
    if (location.country) parts.push(location.country)
    return parts.join(', ') || 'Remote Position'
  }

  const getOfficeTypeIcon = () => {
    switch (location.office_type) {
      case 'remote':
        return <Globe className="w-4 h-4 text-green-600" />
      case 'hybrid':
        return <Building className="w-4 h-4 text-blue-600" />
      case 'office':
        return <Building className="w-4 h-4 text-gray-600" />
      default:
        return <MapPin className="w-4 h-4 text-gray-600" />
    }
  }

  const getOfficeTypeLabel = () => {
    switch (location.office_type) {
      case 'remote':
        return 'Fully Remote'
      case 'hybrid':
        return 'Hybrid (Remote + Office)'
      case 'office':
        return 'On-site Office'
      default:
        return 'Work Location'
    }
  }

  return (
    <div className="space-y-4">
      {/* Location Info */}
      <div className="flex items-start space-x-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div className="shrink-0 mt-1">
          {getOfficeTypeIcon()}
        </div>
        <div className="flex-1">
          <div className="font-medium text-gray-900 dark:text-white mb-1">
            {getOfficeTypeLabel()}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {formatAddress()}
          </div>
          {location.timezone && (
            <div className="text-sm text-gray-500 dark:text-gray-500 mt-1">
              Timezone: {location.timezone}
            </div>
          )}
        </div>
      </div>

      {/* Map */}
      {location.latitude && location.longitude ? (
        <div className="rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
          <MapContainer
            center={position}
            zoom={13}
            style={{ height, width: '100%' }}
            className="z-0"
          >
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
            <Marker position={position} icon={defaultIcon}>
              <Popup>
                <div className="p-2">
                  <div className="font-semibold mb-1">{getOfficeTypeLabel()}</div>
                  <div className="text-sm text-gray-600">{formatAddress()}</div>
                </div>
              </Popup>
            </Marker>
          </MapContainer>
        </div>
      ) : (
        <div className="h-64 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center border border-gray-200 dark:border-gray-700">
          <div className="text-center">
            <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <div className="text-gray-600 dark:text-gray-400 font-medium">
              {location.office_type === 'remote' ? 'Work from Anywhere' : 'Location Details Available'}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-500">
              {formatAddress()}
            </div>
          </div>
        </div>
      )}

      {/* Additional Info */}
      {location.is_remote_friendly && location.office_type !== 'remote' && (
        <div className="flex items-center space-x-2 text-sm">
          <Globe className="w-4 h-4 text-green-600" />
          <span className="text-green-600 font-medium">Remote-friendly workplace</span>
        </div>
      )}
    </div>
  )
}