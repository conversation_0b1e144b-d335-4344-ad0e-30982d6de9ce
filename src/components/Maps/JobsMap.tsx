import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet'
import { I<PERSON> } from 'leaflet'
import 'leaflet/dist/leaflet.css'
import { Building, MapPin, Users } from 'lucide-react'

// Fix for default markers in react-leaflet
const defaultIcon = new Icon({
  iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
  shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
})

// Create custom icons for different job types
const createCustomIcon = (color: string) => new Icon({
  iconUrl: `data:image/svg+xml;base64,${btoa(`
    <svg width="25" height="41" viewBox="0 0 25 41" xmlns="http://www.w3.org/2000/svg">
      <path d="M12.5 0C5.6 0 0 5.6 0 12.5c0 12.5 12.5 28.5 12.5 28.5s12.5-16 12.5-28.5C25 5.6 19.4 0 12.5 0z" fill="${color}"/>
      <circle cx="12.5" cy="12.5" r="6" fill="white"/>
    </svg>
  `)}`,
  shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
})

const featuredIcon = createCustomIcon('#3b82f6') // Blue for featured jobs
const regularIcon = createCustomIcon('#10b981') // Green for regular jobs
const companyIcon = createCustomIcon('#8b5cf6') // Purple for companies

interface JobLocation {
  id: string
  latitude: number
  longitude: number
  title: string
  company: string
  location: string
  isFeatured?: boolean
  jobCount?: number
  type: 'job' | 'company'
}

interface JobsMapProps {
  locations: JobLocation[]
  height?: string
  center?: [number, number]
  zoom?: number
}

export default function JobsMap({ 
  locations, 
  height = '400px', 
  center = [40.7128, -74.0060], // Default to NYC
  zoom = 5 
}: JobsMapProps) {
  const getIcon = (location: JobLocation) => {
    if (location.type === 'company') return companyIcon
    return location.isFeatured ? featuredIcon : regularIcon
  }

  const getPopupContent = (location: JobLocation) => {
    if (location.type === 'company') {
      return (
        <div className="p-2 min-w-48">
          <div className="flex items-center space-x-2 mb-2">
            <Building className="w-4 h-4 text-purple-600" />
            <span className="font-semibold text-gray-900">{location.company}</span>
          </div>
          <div className="text-sm text-gray-600 mb-1">{location.location}</div>
          {location.jobCount && (
            <div className="flex items-center space-x-1 text-xs text-gray-500">
              <Users className="w-3 h-3" />
              <span>{location.jobCount} open positions</span>
            </div>
          )}
        </div>
      )
    }

    return (
      <div className="p-2 min-w-48">
        <div className="font-semibold text-gray-900 mb-1">{location.title}</div>
        <div className="text-sm text-blue-600 mb-1">{location.company}</div>
        <div className="flex items-center space-x-1 text-xs text-gray-500">
          <MapPin className="w-3 h-3" />
          <span>{location.location}</span>
        </div>
        {location.isFeatured && (
          <div className="mt-1">
            <span className="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
              Featured
            </span>
          </div>
        )}
      </div>
    )
  }

  if (locations.length === 0) {
    return (
      <div 
        className="bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center border border-gray-200 dark:border-gray-700"
        style={{ height }}
      >
        <div className="text-center">
          <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-2" />
          <div className="text-gray-600 dark:text-gray-400 font-medium">
            No locations to display
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-500">
            Jobs and companies will appear here when available
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
      <MapContainer
        center={center}
        zoom={zoom}
        style={{ height, width: '100%' }}
        className="z-0"
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        {locations.map((location) => (
          <Marker
            key={location.id}
            position={[location.latitude, location.longitude]}
            icon={getIcon(location)}
          >
            <Popup>
              {getPopupContent(location)}
            </Popup>
          </Marker>
        ))}
      </MapContainer>
    </div>
  )
}