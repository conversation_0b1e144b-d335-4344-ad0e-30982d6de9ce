import React, { useState } from 'react'
import { X } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import GoogleButton from './GoogleButton'

interface AuthModalProps {
  isOpen: boolean
  onClose: () => void
}

export default function AuthModal({ isOpen, onClose }: AuthModalProps) {
  const [isSignIn, setIsSignIn] = useState(true)
  const [error, setError] = useState('')
  const { signInWithGoogle } = useAuth()

  if (!isOpen) return null



  const handleGoogleSignIn = async () => {
    try {
      setError('')
      await signInWithGoogle()
      // Don't close modal here as we're redirecting
    } catch (error: any) {
      setError(error.message || 'Google authentication failed')
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-xl max-w-md w-full p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            {isSignIn ? 'Sign In to Remotify.work' : 'Join Remotify.work'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="text-center mb-6">
          <p className="text-gray-600 dark:text-gray-400">
            {isSignIn 
              ? 'Welcome back! Sign in with your Google account to access premium remote job opportunities.' 
              : 'Get started with Google to access premium remote job opportunities.'
            }
          </p>
        </div>

        {/* Google Sign In Button */}
        <div className="mb-6">
          <GoogleButton 
            onClick={handleGoogleSignIn}
            text={isSignIn ? 'Continue with Google' : 'Sign up with Google'}
          />
        </div>

        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-lg text-sm mb-4">
            {error}
          </div>
        )}

        <div className="mt-6 text-center">
          <button
            onClick={() => setIsSignIn(!isSignIn)}
            className="text-green-600 dark:text-green-400 hover:underline"
          >
            {isSignIn ? "Don't have an account? Sign up" : "Already have an account? Sign in"}
          </button>
        </div>
      </div>
    </div>
  )
}