import React from 'react'
import { motion } from 'framer-motion'
import { 
  Check,
  Star,
  Crown,
  Zap,
  Building,
  BarChart3,
  Palette,
  HeadphonesIcon,
  MapPin,
  Users,
  Mail,
  Globe,
  ArrowRight
} from 'lucide-react'
import { <PERSON> } from 'react-router-dom'
import { But<PERSON> } from '../components/ui/button'
import { Badge } from '../components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'

const pricingPlans = [
  {
    name: 'Free',
    price: '$0',
    period: 'forever',
    description: 'Perfect for testing the waters',
    icon: Building,
    color: 'gray',
    features: [
      '1 job posting per month',
      'Basic job listing',
      'Standard visibility',
      '30-day listing duration',
      'Email support',
      'Basic analytics'
    ],
    limitations: [
      'No featured placement',
      'No custom branding',
      'Limited applicant tracking'
    ],
    cta: 'Get Started',
    popular: false
  },
  {
    name: 'Professional',
    price: '$99',
    period: 'per month',
    description: 'Most popular for growing companies',
    icon: Star,
    color: 'blue',
    features: [
      '5 job postings per month',
      'Featured job placement',
      'Premium visibility boost',
      '60-day listing duration',
      'Priority email support',
      'Advanced analytics dashboard',
      'Custom company branding',
      'Applicant tracking system',
      'Social media promotion'
    ],
    limitations: [],
    cta: 'Start Free Trial',
    popular: true,
    badge: 'Most Popular'
  },
  {
    name: 'Enterprise',
    price: '$299',
    period: 'per month',
    description: 'For large organizations with high volume',
    icon: Crown,
    color: 'purple',
    features: [
      'Unlimited job postings',
      'Priority featured placement',
      'Maximum visibility boost',
      '90-day listing duration',
      'Dedicated account manager',
      'Custom analytics reports',
      'Full custom branding suite',
      'Advanced ATS integration',
      'Multi-channel promotion',
      'Bulk posting tools',
      'API access',
      'Custom integrations'
    ],
    limitations: [],
    cta: 'Contact Sales',
    popular: false
  }
]

const features = [
  {
    icon: MapPin,
    title: 'Global Reach',
    description: 'Connect with remote talent from 180+ countries'
  },
  {
    icon: BarChart3,
    title: 'Advanced Analytics',
    description: 'Track performance with detailed insights and reports'
  },
  {
    icon: Users,
    title: 'Quality Candidates',
    description: 'Access pre-vetted remote professionals'
  },
  {
    icon: Zap,
    title: 'Fast Hiring',
    description: 'Reduce time-to-hire with streamlined processes'
  }
]

const testimonials = [
  {
    name: 'Sarah Chen',
    role: 'Head of Talent',
    company: 'TechFlow Inc.',
    content: 'Remotify.work helped us find amazing remote developers in just 2 weeks. The quality of candidates is exceptional.',
    avatar: '/images/business_team_remote_collaboration_hybrid_meeting_modern_workplace.jpg'
  },
  {
    name: 'Marcus Rodriguez',
    role: 'Startup Founder',
    company: 'InnovateLab',
    content: 'As a startup, we needed cost-effective hiring. The Professional plan gave us everything we needed to build our remote team.',
    avatar: '/images/business_team_hybrid_remote_collaboration_meeting.jpg'
  },
  {
    name: 'Emily Watson',
    role: 'People Operations',
    company: 'GlobalTech Solutions',
    content: 'The analytics dashboard provides incredible insights. We can track our hiring metrics and optimize our job postings effectively.',
    avatar: '/images/business_team_remote_collaboration_video_meeting_modern_office.jpg'
  }
]

export default function PricingPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <section className="bg-linear-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-900 dark:to-blue-950 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <Badge className="inline-flex items-center space-x-2 bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800 mb-6">
              <Crown className="w-4 h-4" />
              <span>Premium Job Board</span>
            </Badge>
            
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Simple, Transparent
              <span className="block text-transparent bg-clip-text bg-linear-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400">
                Pricing for Everyone
              </span>
            </h1>
            
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
              Choose the perfect plan for your hiring needs. From startups to enterprises, 
              we have flexible options that scale with your business.
            </p>

            <div className="flex items-center justify-center space-x-6 text-sm text-gray-600 dark:text-gray-400">
              <div className="flex items-center">
                <Check className="w-4 h-4 text-green-600 mr-2" />
                <span>No setup fees</span>
              </div>
              <div className="flex items-center">
                <Check className="w-4 h-4 text-green-600 mr-2" />
                <span>Cancel anytime</span>
              </div>
              <div className="flex items-center">
                <Check className="w-4 h-4 text-green-600 mr-2" />
                <span>30-day free trial</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {pricingPlans.map((plan, index) => {
            const Icon = plan.icon
            return (
              <motion.div
                key={plan.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className={`relative ${
                  plan.popular ? 'lg:scale-105 lg:-translate-y-4' : ''
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-linear-to-r from-blue-600 to-indigo-600 text-white px-4 py-1">
                      {plan.badge}
                    </Badge>
                  </div>
                )}
                
                <Card className={`h-full transition-all duration-200 hover:shadow-lg ${
                  plan.popular 
                    ? 'border-blue-200 dark:border-blue-800 shadow-lg' 
                    : 'border-gray-200 dark:border-gray-700'
                }`}>
                  <CardHeader className="text-center pb-4">
                    <div className={`inline-flex items-center justify-center w-12 h-12 rounded-xl mb-4 ${
                      plan.color === 'gray' ? 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400' :
                      plan.color === 'blue' ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400' :
                      'bg-purple-100 dark:bg-purple-900/50 text-purple-600 dark:text-purple-400'
                    }`}>
                      <Icon className="w-6 h-6" />
                    </div>
                    
                    <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {plan.name}
                    </CardTitle>
                    
                    <div className="mb-4">
                      <span className="text-4xl font-bold text-gray-900 dark:text-white">
                        {plan.price}
                      </span>
                      <span className="text-gray-600 dark:text-gray-400 ml-2">
                        {plan.period}
                      </span>
                    </div>
                    
                    <p className="text-gray-600 dark:text-gray-400 text-sm">
                      {plan.description}
                    </p>
                  </CardHeader>
                  
                  <CardContent className="pt-0">
                    <Button 
                      className={`w-full mb-6 ${
                        plan.popular 
                          ? 'bg-linear-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white' 
                          : 'bg-gray-900 dark:bg-white text-white dark:text-gray-900 hover:bg-gray-800 dark:hover:bg-gray-100'
                      }`}
                      size="lg"
                    >
                      {plan.cta}
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                    
                    <div className="space-y-3">
                      <div className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                        What's included:
                      </div>
                      
                      {plan.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center">
                          <Check className="w-4 h-4 text-green-600 mr-3 shrink-0" />
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {feature}
                          </span>
                        </div>
                      ))}
                      
                      {plan.limitations.length > 0 && (
                        <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                          <div className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                            Not included:
                          </div>
                          {plan.limitations.map((limitation, limitationIndex) => (
                            <div key={limitationIndex} className="flex items-center opacity-60">
                              <span className="w-4 h-4 mr-3 shrink-0 text-gray-400">×</span>
                              <span className="text-sm text-gray-600 dark:text-gray-400">
                                {limitation}
                              </span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </div>
      </section>

      {/* Features Section */}
      <section className="bg-white dark:bg-gray-800 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Why Choose Remotify.work?
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Built specifically for remote hiring with features that make finding 
              and managing remote talent effortless.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-xl bg-linear-to-br from-blue-500 to-blue-600 text-white mb-4">
                    <Icon className="w-6 h-6" />
                  </div>
                  
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {feature.title}
                  </h3>
                  
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    {feature.description}
                  </p>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Trusted by Growing Companies
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              See what our customers say about their hiring success with Remotify.work.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full">
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      <img
                        src={testimonial.avatar}
                        alt={testimonial.name}
                        className="w-12 h-12 rounded-full object-cover mr-4"
                      />
                      <div>
                        <div className="font-semibold text-gray-900 dark:text-white">
                          {testimonial.name}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {testimonial.role} at {testimonial.company}
                        </div>
                      </div>
                    </div>
                    
                    <p className="text-gray-600 dark:text-gray-400 italic">
                      "{testimonial.content}"
                    </p>
                    
                    <div className="flex items-center mt-4">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-linear-to-r from-blue-600 to-indigo-600 py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Ready to Find Your Next Remote Hire?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Join thousands of companies building amazing remote teams with Remotify.work.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/post-job">
                <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                  Start Free Trial
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
              <Link to="/contact">
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                  <Mail className="w-5 h-5 mr-2" />
                  Contact Sales
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}