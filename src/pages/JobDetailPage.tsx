import React from 'react'
import { useParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { motion } from 'framer-motion'
import { 
  MapPin,
  Building,
  Clock,
  DollarSign,
  Users,
  ExternalLink,
  Bookmark,
  Share2,
  Calendar,
  Globe,
  CheckCircle,
  Star,
  Eye,
  ArrowLeft
} from 'lucide-react'
import { Link } from 'react-router-dom'
import { Button } from '../components/ui/button'
import { Badge } from '../components/ui/badge'
import { Card, CardContent } from '../components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '../components/ui/avatar'
import { Separator } from '../components/ui/separator'
import { supabase, Job } from '../lib/supabase'
import { formatDistanceToNow } from 'date-fns'
import JobLocationMap from '../components/Maps/JobLocationMap'
import ApplyButton from '../components/Jobs/ApplyButton'

export default function JobDetailPage() {
  const { slug } = useParams<{ slug: string }>()

  const { data: job, isLoading, error } = useQuery({
    queryKey: ['job', slug],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('jobs')
        .select(`
          *,
          company:companies(*),
          job_locations(*),
          featured_jobs(*)
        `)
        .eq('slug', slug)
        .eq('is_active', true)
        .single()
      
      if (error) throw error
      return data as Job
    },
  })

  // Track job view
  React.useEffect(() => {
    if (job) {
      // Update view count
      supabase
        .from('jobs')
        .update({ view_count: job.view_count + 1 })
        .eq('id', job.id)
        .then(() => {
          // Track view in analytics
          const today = new Date().toISOString().split('T')[0]
          supabase
            .from('job_analytics')
            .upsert({
              job_id: job.id,
              date: today,
              views_count: 1
            }, {
              onConflict: 'job_id,date',
              ignoreDuplicates: false
            })
        })
    }
  }, [job])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Loading job details...</p>
        </div>
      </div>
    )
  }

  if (error || !job) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Job Not Found</h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">The job you're looking for doesn't exist or has been removed.</p>
          <Link to="/">
            <Button>Browse All Jobs</Button>
          </Link>
        </div>
      </div>
    )
  }

  const formatSalary = () => {
    if (!job.salary_min && !job.salary_max) return null
    
    const currency = job.salary_currency === 'USD' ? '$' : job.salary_currency
    
    if (job.salary_min && job.salary_max) {
      return `${currency}${job.salary_min.toLocaleString()}k - ${currency}${job.salary_max.toLocaleString()}k`
    } else if (job.salary_min) {
      return `${currency}${job.salary_min.toLocaleString()}k+`
    } else if (job.salary_max) {
      return `Up to ${currency}${job.salary_max.toLocaleString()}k`
    }
    return null
  }

  const timeAgo = formatDistanceToNow(new Date(job.posted_at), { addSuffix: true })
  const salary = formatSalary()

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <div className="mb-6">
          <Link to="/">
            <Button variant="ghost" className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Jobs
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Job Header */}
            <Card className={job.is_featured ? 'border-blue-200 dark:border-blue-800 bg-linear-to-br from-blue-50/50 to-indigo-50/50 dark:from-blue-950/20 dark:to-indigo-950/20' : ''}>
              <CardContent className="p-8">
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-start space-x-4">
                    <Avatar className="w-16 h-16 border-2 border-white dark:border-gray-800 shadow-md">
                      <AvatarImage 
                        src={job.company?.logo_url || '/images/modern_tech_company_office_workspace.jpg'} 
                        alt={job.company?.name} 
                      />
                      <AvatarFallback className="bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 font-semibold text-xl">
                        {job.company?.name?.charAt(0) || 'C'}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                          {job.title}
                        </h1>
                        {job.is_featured && (
                          <Badge className="bg-linear-to-r from-blue-600 to-indigo-600 text-white">
                            <Star className="w-3 h-3 mr-1" />
                            Featured
                          </Badge>
                        )}
                      </div>
                      
                      <Link 
                        to={`/company/${job.company_id}`}
                        className="text-xl text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 font-medium"
                      >
                        {job.company?.name}
                      </Link>
                      
                      {job.company?.is_verified && (
                        <div className="flex items-center mt-2">
                          <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400 mr-1" />
                          <span className="text-sm text-green-600 dark:text-green-400 font-medium">Verified Company</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <Bookmark className="w-4 h-4 mr-2" />
                      Save
                    </Button>
                    <Button variant="outline" size="sm">
                      <Share2 className="w-4 h-4 mr-2" />
                      Share
                    </Button>
                  </div>
                </div>

                {/* Job Meta */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-6">
                  <div className="flex items-center">
                    <MapPin className="w-5 h-5 text-gray-400 mr-2" />
                    <div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">Location</div>
                      <div className="font-medium text-gray-900 dark:text-white">{job.location || 'Remote'}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <Building className="w-5 h-5 text-gray-400 mr-2" />
                    <div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">Type</div>
                      <div className="font-medium text-gray-900 dark:text-white">{job.employment_type}</div>
                    </div>
                  </div>
                  
                  {job.experience_level && (
                    <div className="flex items-center">
                      <Users className="w-5 h-5 text-gray-400 mr-2" />
                      <div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">Experience</div>
                        <div className="font-medium text-gray-900 dark:text-white">{job.experience_level}</div>
                      </div>
                    </div>
                  )}
                  
                  <div className="flex items-center">
                    <Clock className="w-5 h-5 text-gray-400 mr-2" />
                    <div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">Posted</div>
                      <div className="font-medium text-gray-900 dark:text-white">{timeAgo}</div>
                    </div>
                  </div>
                </div>

                {/* Salary */}
                {salary && (
                  <div className="flex items-center mb-6 p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                    <DollarSign className="w-6 h-6 text-green-600 dark:text-green-400 mr-3" />
                    <div>
                      <div className="text-sm text-green-600 dark:text-green-400 font-medium">Salary Range</div>
                      <div className="text-xl font-bold text-green-700 dark:text-green-300">{salary}</div>
                    </div>
                  </div>
                )}

                {/* Tags */}
                {job.tags && job.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {job.tags.map((tag) => (
                      <Badge 
                        key={tag} 
                        variant="secondary" 
                        className="bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Job Description */}
            <Card>
              <CardContent className="p-8">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Job Description</h2>
                <div 
                  className="prose dark:prose-invert max-w-none"
                  dangerouslySetInnerHTML={{ __html: job.description }}
                />
              </CardContent>
            </Card>

            {/* Requirements */}
            {job.requirements && (
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Requirements</h2>
                  <div 
                    className="prose dark:prose-invert max-w-none"
                    dangerouslySetInnerHTML={{ __html: job.requirements }}
                  />
                </CardContent>
              </Card>
            )}

            {/* Benefits */}
            {job.benefits && job.benefits.length > 0 && (
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Benefits & Perks</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {job.benefits.map((benefit, index) => (
                      <div key={index} className="flex items-center">
                        <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mr-3" />
                        <span className="text-gray-700 dark:text-gray-300">{benefit}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Location Map */}
            {job.job_locations && (
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Location</h2>
                  <JobLocationMap location={job.job_locations} />
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Apply Card */}
            <Card className="sticky top-24">
              <CardContent className="p-6">
                <div className="text-center mb-6">
                  <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">Ready to apply?</div>
                  <div className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                    Join the Team
                  </div>
                </div>
                
                <ApplyButton
                  jobId={job.id}
                  jobTitle={job.title}
                  externalUrl={job.external_apply_url}
                  className="w-full bg-linear-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
                />
                
                {job.how_to_apply && (
                  <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="text-sm font-medium text-gray-900 dark:text-white mb-2">How to Apply:</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">{job.how_to_apply}</div>
                  </div>
                )}
                
                <Separator className="my-6" />
                
                <div className="text-center text-sm text-gray-500 dark:text-gray-400">
                  <div className="flex items-center justify-center space-x-4 mb-2">
                    <div className="flex items-center">
                      <Eye className="w-4 h-4 mr-1" />
                      <span>{job.view_count} views</span>
                    </div>
                    {job.application_count > 0 && (
                      <div className="flex items-center">
                        <Users className="w-4 h-4 mr-1" />
                        <span>{job.application_count} applied</span>
                      </div>
                    )}
                  </div>
                  <div>Job posted {timeAgo}</div>
                </div>
              </CardContent>
            </Card>

            {/* Company Info */}
            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">About the Company</h3>
                
                <div className="flex items-center space-x-3 mb-4">
                  <Avatar className="w-12 h-12">
                    <AvatarImage 
                      src={job.company?.logo_url || '/images/modern_tech_company_office_workspace.jpg'} 
                      alt={job.company?.name} 
                    />
                    <AvatarFallback className="bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 font-semibold">
                      {job.company?.name?.charAt(0) || 'C'}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white">{job.company?.name}</h4>
                    {job.company?.industry && (
                      <p className="text-sm text-gray-600 dark:text-gray-400">{job.company?.industry}</p>
                    )}
                  </div>
                </div>
                
                {job.company?.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-3">
                    {job.company.description}
                  </p>
                )}
                
                <div className="space-y-2 text-sm">
                  {job.company?.size_range && (
                    <div className="flex items-center">
                      <Users className="w-4 h-4 text-gray-400 mr-2" />
                      <span className="text-gray-600 dark:text-gray-400">{job.company.size_range} employees</span>
                    </div>
                  )}
                  
                  {job.company?.location && (
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 text-gray-400 mr-2" />
                      <span className="text-gray-600 dark:text-gray-400">{job.company.location}</span>
                    </div>
                  )}
                  
                  {job.company?.website_url && (
                    <div className="flex items-center">
                      <Globe className="w-4 h-4 text-gray-400 mr-2" />
                      <a 
                        href={job.company.website_url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200"
                      >
                        Visit Website
                      </a>
                    </div>
                  )}
                </div>
                
                <Link to={`/company/${job.company_id}`} className="mt-4 block">
                  <Button variant="outline" className="w-full">
                    View Company Profile
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}