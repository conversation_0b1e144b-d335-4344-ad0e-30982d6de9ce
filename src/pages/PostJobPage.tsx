import React from 'react'
import { useState } from 'react'
import { motion } from 'framer-motion'
import { useAuth } from '../contexts/AuthContext'
import { 
  Building,
  MapPin,
  DollarSign,
  Clock,
  FileText,
  Tag,
  Users,
  Star,
  Globe,
  Calendar,
  Briefcase,
  Upload,
  Eye,
  Save,
  Send
} from 'lucide-react'
import { But<PERSON> } from '../components/ui/button'
import { Input } from '../components/ui/input'
import { Label } from '../components/ui/label'
import { Textarea } from '../components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { Badge } from '../components/ui/badge'
import { Checkbox } from '../components/ui/checkbox'
import { RadioGroup, RadioGroupItem } from '../components/ui/radio-group'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs'
import { Separator } from '../components/ui/separator'

const jobCategories = [
  'Software Development',
  'Data Science',
  'Design',
  'Marketing',
  'Sales',
  'Customer Support',
  'Product Management',
  'DevOps',
  'Finance',
  'Human Resources'
]

const skillTags = [
  'JavaScript', 'Python', 'React', 'Node.js', 'TypeScript', 'AWS', 'Docker',
  'PostgreSQL', 'MongoDB', 'GraphQL', 'REST API', 'Machine Learning',
  'Data Analysis', 'Figma', 'Photoshop', 'UI/UX', 'Product Strategy',
  'Agile', 'Scrum', 'Git', 'CI/CD', 'Kubernetes', 'Terraform'
]

const benefits = [
  'Health Insurance',
  'Dental Insurance',
  'Vision Insurance',
  '401(k) Matching',
  'Flexible PTO',
  'Remote Work Stipend',
  'Professional Development Budget',
  'Gym Membership',
  'Mental Health Support',
  'Parental Leave',
  'Stock Options',
  'Annual Bonus',
  'Conference Attendance',
  'Home Office Setup',
  'Internet Reimbursement'
]

export default function PostJobPage() {
  const { user } = useAuth()
  const [currentStep, setCurrentStep] = useState(1)
  const [jobData, setJobData] = useState({
    title: '',
    category: '',
    description: '',
    requirements: '',
    location: '',
    locationType: 'remote',
    employmentType: 'full-time',
    experienceLevel: '',
    salaryMin: '',
    salaryMax: '',
    salaryCurrency: 'USD',
    selectedBenefits: [],
    selectedSkills: [],
    applicationUrl: '',
    howToApply: '',
    featured: false,
    companyName: '',
    companyWebsite: '',
    companyDescription: ''
  })

  const totalSteps = 4

  const handleInputChange = (field: string, value: any) => {
    setJobData(prev => ({ ...prev, [field]: value }))
  }

  const handleBenefitToggle = (benefit: string) => {
    setJobData(prev => ({
      ...prev,
      selectedBenefits: prev.selectedBenefits.includes(benefit)
        ? prev.selectedBenefits.filter(b => b !== benefit)
        : [...prev.selectedBenefits, benefit]
    }))
  }

  const handleSkillToggle = (skill: string) => {
    setJobData(prev => ({
      ...prev,
      selectedSkills: prev.selectedSkills.includes(skill)
        ? prev.selectedSkills.filter(s => s !== skill)
        : [...prev.selectedSkills, skill]
    }))
  }

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = () => {
    // Handle job submission
    console.log('Submitting job:', jobData)
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Please Sign In
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            You need to be signed in to post a job.
          </p>
          <Button>Sign In to Continue</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Post a Remote Job
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            Reach thousands of qualified remote professionals worldwide
          </p>
          
          {/* Progress Bar */}
          <div className="mt-8">
            <div className="flex items-center justify-center space-x-4">
              {[...Array(totalSteps)].map((_, index) => (
                <div key={index} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                    index + 1 <= currentStep 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                  }`}>
                    {index + 1}
                  </div>
                  {index < totalSteps - 1 && (
                    <div className={`w-12 h-1 mx-2 ${
                      index + 1 < currentStep 
                        ? 'bg-blue-600' 
                        : 'bg-gray-200 dark:bg-gray-700'
                    }`} />
                  )}
                </div>
              ))}
            </div>
            <div className="flex justify-center mt-2 text-sm text-gray-600 dark:text-gray-400">
              Step {currentStep} of {totalSteps}
            </div>
          </div>
        </motion.div>

        {/* Form */}
        <Card>
          <CardContent className="p-8">
            {/* Step 1: Job Basics */}
            {currentStep === 1 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Job Details
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Let's start with the basic information about your job posting
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <Label htmlFor="title">Job Title *</Label>
                    <Input
                      id="title"
                      value={jobData.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      placeholder="e.g. Senior React Developer"
                      className="mt-2"
                    />
                  </div>

                  <div>
                    <Label htmlFor="category">Category *</Label>
                    <Select onValueChange={(value) => handleInputChange('category', value)}>
                      <SelectTrigger className="mt-2">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {jobCategories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="employmentType">Employment Type *</Label>
                    <Select onValueChange={(value) => handleInputChange('employmentType', value)}>
                      <SelectTrigger className="mt-2">
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="full-time">Full-time</SelectItem>
                        <SelectItem value="part-time">Part-time</SelectItem>
                        <SelectItem value="contract">Contract</SelectItem>
                        <SelectItem value="freelance">Freelance</SelectItem>
                        <SelectItem value="internship">Internship</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="experienceLevel">Experience Level</Label>
                    <Select onValueChange={(value) => handleInputChange('experienceLevel', value)}>
                      <SelectTrigger className="mt-2">
                        <SelectValue placeholder="Select level" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="entry">Entry Level</SelectItem>
                        <SelectItem value="mid">Mid Level</SelectItem>
                        <SelectItem value="senior">Senior Level</SelectItem>
                        <SelectItem value="lead">Lead/Principal</SelectItem>
                        <SelectItem value="executive">Executive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Location Type *</Label>
                    <RadioGroup 
                      value={jobData.locationType} 
                      onValueChange={(value) => handleInputChange('locationType', value)}
                      className="mt-2"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="remote" id="remote" />
                        <Label htmlFor="remote">Fully Remote</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="hybrid" id="hybrid" />
                        <Label htmlFor="hybrid">Hybrid</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="onsite" id="onsite" />
                        <Label htmlFor="onsite">On-site</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  {jobData.locationType !== 'remote' && (
                    <div>
                      <Label htmlFor="location">Location</Label>
                      <Input
                        id="location"
                        value={jobData.location}
                        onChange={(e) => handleInputChange('location', e.target.value)}
                        placeholder="e.g. San Francisco, CA or London, UK"
                        className="mt-2"
                      />
                    </div>
                  )}
                </div>
              </motion.div>
            )}

            {/* Step 2: Job Description */}
            {currentStep === 2 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Job Description
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Describe the role, responsibilities, and what you're looking for
                  </p>
                </div>

                <div className="space-y-6">
                  <div>
                    <Label htmlFor="description">Job Description *</Label>
                    <Textarea
                      id="description"
                      value={jobData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="Describe the role, responsibilities, and what the candidate will be working on..."
                      className="mt-2 min-h-40"
                    />
                  </div>

                  <div>
                    <Label htmlFor="requirements">Requirements & Qualifications</Label>
                    <Textarea
                      id="requirements"
                      value={jobData.requirements}
                      onChange={(e) => handleInputChange('requirements', e.target.value)}
                      placeholder="List the required skills, experience, and qualifications..."
                      className="mt-2 min-h-32"
                    />
                  </div>

                  <div>
                    <Label>Required Skills & Technologies</Label>
                    <div className="mt-2 flex flex-wrap gap-2 max-h-40 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-lg p-3">
                      {skillTags.map((skill) => (
                        <Button
                          key={skill}
                          type="button"
                          variant={jobData.selectedSkills.includes(skill) ? "default" : "outline"}
                          size="sm"
                          onClick={() => handleSkillToggle(skill)}
                          className="text-xs h-8"
                        >
                          {skill}
                        </Button>
                      ))}
                    </div>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                      Select relevant skills and technologies
                    </p>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Step 3: Compensation & Benefits */}
            {currentStep === 3 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Compensation & Benefits
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Attract top talent with competitive compensation and benefits
                  </p>
                </div>

                <div className="space-y-6">
                  {/* Salary Range */}
                  <div>
                    <Label>Salary Range (Optional but Recommended)</Label>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2">
                      <div>
                        <Label htmlFor="salaryMin" className="text-sm">Minimum</Label>
                        <Input
                          id="salaryMin"
                          type="number"
                          value={jobData.salaryMin}
                          onChange={(e) => handleInputChange('salaryMin', e.target.value)}
                          placeholder="50000"
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="salaryMax" className="text-sm">Maximum</Label>
                        <Input
                          id="salaryMax"
                          type="number"
                          value={jobData.salaryMax}
                          onChange={(e) => handleInputChange('salaryMax', e.target.value)}
                          placeholder="80000"
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="salaryCurrency" className="text-sm">Currency</Label>
                        <Select onValueChange={(value) => handleInputChange('salaryCurrency', value)}>
                          <SelectTrigger className="mt-1">
                            <SelectValue placeholder="USD" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="USD">USD</SelectItem>
                            <SelectItem value="EUR">EUR</SelectItem>
                            <SelectItem value="GBP">GBP</SelectItem>
                            <SelectItem value="CAD">CAD</SelectItem>
                            <SelectItem value="AUD">AUD</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  {/* Benefits */}
                  <div>
                    <Label>Benefits & Perks</Label>
                    <div className="mt-2 grid grid-cols-2 md:grid-cols-3 gap-2 max-h-60 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-lg p-4">
                      {benefits.map((benefit) => (
                        <div key={benefit} className="flex items-center space-x-2">
                          <Checkbox
                            id={benefit}
                            checked={jobData.selectedBenefits.includes(benefit)}
                            onCheckedChange={() => handleBenefitToggle(benefit)}
                          />
                          <Label htmlFor={benefit} className="text-sm cursor-pointer">
                            {benefit}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Step 4: Application Process */}
            {currentStep === 4 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Application Process
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Set up how candidates can apply for this position
                  </p>
                </div>

                <div className="space-y-6">
                  <div>
                    <Label htmlFor="applicationUrl">Application URL (Optional)</Label>
                    <Input
                      id="applicationUrl"
                      value={jobData.applicationUrl}
                      onChange={(e) => handleInputChange('applicationUrl', e.target.value)}
                      placeholder="https://yourcompany.com/apply"
                      className="mt-2"
                    />
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                      Leave blank to receive applications through Remotify.work
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="howToApply">How to Apply</Label>
                    <Textarea
                      id="howToApply"
                      value={jobData.howToApply}
                      onChange={(e) => handleInputChange('howToApply', e.target.value)}
                      placeholder="Please send your resume and a cover letter explaining why you're a great fit for this role..."
                      className="mt-2"
                    />
                  </div>

                  {/* Company Information */}
                  <Separator className="my-6" />
                  
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Company Information
                    </h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="companyName">Company Name *</Label>
                        <Input
                          id="companyName"
                          value={jobData.companyName}
                          onChange={(e) => handleInputChange('companyName', e.target.value)}
                          placeholder="Your Company Name"
                          className="mt-2"
                        />
                      </div>

                      <div>
                        <Label htmlFor="companyWebsite">Company Website</Label>
                        <Input
                          id="companyWebsite"
                          value={jobData.companyWebsite}
                          onChange={(e) => handleInputChange('companyWebsite', e.target.value)}
                          placeholder="https://yourcompany.com"
                          className="mt-2"
                        />
                      </div>

                      <div className="md:col-span-2">
                        <Label htmlFor="companyDescription">Company Description</Label>
                        <Textarea
                          id="companyDescription"
                          value={jobData.companyDescription}
                          onChange={(e) => handleInputChange('companyDescription', e.target.value)}
                          placeholder="Brief description of your company, culture, and mission..."
                          className="mt-2"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Featured Option */}
                  <div className="border border-blue-200 dark:border-blue-800 rounded-lg p-4 bg-blue-50 dark:bg-blue-950/20">
                    <div className="flex items-center space-x-3">
                      <Checkbox
                        id="featured"
                        checked={jobData.featured}
                        onCheckedChange={(checked) => handleInputChange('featured', checked)}
                      />
                      <div className="flex-1">
                        <Label htmlFor="featured" className="flex items-center cursor-pointer">
                          <Star className="w-4 h-4 text-yellow-500 mr-2" />
                          Make this a Featured Job (+$50)
                        </Label>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          Featured jobs get 3x more visibility and are highlighted in search results
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Navigation Buttons */}
            <div className="flex items-center justify-between pt-8 border-t border-gray-200 dark:border-gray-700 mt-8">
              <Button
                variant="outline"
                onClick={prevStep}
                disabled={currentStep === 1}
                className="flex items-center"
              >
                Previous
              </Button>

              <div className="flex items-center space-x-3">
                <Button variant="outline" className="flex items-center">
                  <Save className="w-4 h-4 mr-2" />
                  Save Draft
                </Button>
                
                {currentStep < totalSteps ? (
                  <Button onClick={nextStep} className="flex items-center">
                    Next
                  </Button>
                ) : (
                  <Button onClick={handleSubmit} className="bg-linear-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white flex items-center">
                    <Send className="w-4 h-4 mr-2" />
                    Publish Job
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Pricing Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mt-8"
        >
          <Card className="border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-950/20">
            <CardContent className="p-6">
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Transparent Pricing
                </h3>
                <div className="flex items-center justify-center space-x-8 text-sm text-gray-600 dark:text-gray-400">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mr-2" />
                    Standard Job: $49
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2" />
                    Featured Job: $99
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-600 rounded-full mr-2" />
                    30-day guarantee
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}