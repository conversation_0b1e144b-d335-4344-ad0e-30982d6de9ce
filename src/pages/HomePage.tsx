import React from 'react'
import { useState, useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import { supabase, Job } from '../lib/supabase'
import JobCard from '../components/Jobs/JobCard'
import JobFilters from '../components/Jobs/JobFilters'
import HeroSection from '../components/Home/HeroSection'
import FeaturedCompanies from '../components/Home/FeaturedCompanies'
import StatsSection from '../components/Home/StatsSection'
import EmailSubscription from '../components/EmailSubscription/SubscriptionForm'
import { Loader2, MapPin, Search } from 'lucide-react'
import { Input } from '../components/ui/input'
import { Button } from '../components/ui/button'

interface JobFilters {
  search: string
  location: string
  employment_type: string[]
  experience_level: string[]
  salary_min: number
  is_featured: boolean
  tags: string[]
}

export default function HomePage() {
  const [filters, setFilters] = useState<JobFilters>({
    search: '',
    location: '',
    employment_type: [],
    experience_level: [],
    salary_min: 0,
    is_featured: false,
    tags: []
  })
  const [showFilters, setShowFilters] = useState(false)

  // Fetch jobs with filters
  const { data: jobs, isLoading, error } = useQuery({
    queryKey: ['jobs', filters],
    queryFn: async () => {
      let query = supabase
        .from('jobs')
        .select(`
          *,
          company:companies(*),
          job_locations(*),
          featured_jobs(*)
        `)
        .eq('is_active', true)
        .order('posted_at', { ascending: false })

      // Apply search filter
      if (filters.search) {
        query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`)
      }

      // Apply location filter
      if (filters.location) {
        query = query.or(`location.ilike.%${filters.location}%`)
      }

      // Apply employment type filter
      if (filters.employment_type.length > 0) {
        query = query.in('employment_type', filters.employment_type)
      }

      // Apply experience level filter
      if (filters.experience_level.length > 0) {
        query = query.in('experience_level', filters.experience_level)
      }

      // Apply salary filter
      if (filters.salary_min > 0) {
        query = query.gte('salary_min', filters.salary_min)
      }

      // Apply featured filter
      if (filters.is_featured) {
        query = query.eq('is_featured', true)
      }

      const { data, error } = await query.limit(50)
      
      if (error) throw error
      return data as Job[]
    },
  })

  // Separate featured and regular jobs
  const { featuredJobs, regularJobs } = useMemo(() => {
    if (!jobs) return { featuredJobs: [], regularJobs: [] }
    
    const featured = jobs.filter(job => job.is_featured || job.featured_jobs)
    const regular = jobs.filter(job => !job.is_featured && !job.featured_jobs)
    
    return { featuredJobs: featured, regularJobs: regular }
  }, [jobs])

  const handleFilterChange = (newFilters: Partial<JobFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <HeroSection />

      {/* Featured Companies */}
      <FeaturedCompanies />

      {/* Stats Section */}
      <StatsSection />

      {/* Jobs Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Filters */}
          <div className="lg:w-80">
            <JobFilters 
              filters={filters} 
              onFiltersChange={handleFilterChange}
              showMobile={showFilters}
              onToggleMobile={() => setShowFilters(!showFilters)}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Search Bar */}
            <div className="mb-8">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <Input
                    type="text"
                    placeholder="Search jobs, companies, technologies..."
                    value={filters.search}
                    onChange={(e) => handleFilterChange({ search: e.target.value })}
                    className="pl-10 h-12 text-lg"
                  />
                </div>
                <div className="relative sm:w-64">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <Input
                    type="text"
                    placeholder="Location"
                    value={filters.location}
                    onChange={(e) => handleFilterChange({ location: e.target.value })}
                    className="pl-10 h-12"
                  />
                </div>
                <Button 
                  onClick={() => setShowFilters(!showFilters)}
                  variant="outline"
                  className="lg:hidden h-12 px-6"
                >
                  Filters
                </Button>
              </div>
            </div>

            {/* Jobs List */}
            <div className="space-y-8">
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
                  <span className="ml-2 text-gray-600 dark:text-gray-400">Loading amazing remote jobs...</span>
                </div>
              ) : error ? (
                <div className="text-center py-12">
                  <p className="text-red-600 dark:text-red-400">Error loading jobs. Please try again.</p>
                </div>
              ) : (
                <>
                  {/* Featured Jobs */}
                  {featuredJobs.length > 0 && (
                    <div>
                      <div className="flex items-center justify-between mb-6">
                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                          Featured Jobs
                        </h2>
                        <div className="h-px bg-linear-to-r from-blue-600 to-purple-600 flex-1 ml-6" />
                      </div>
                      <div className="grid gap-6">
                        {featuredJobs.map((job) => (
                          <JobCard key={job.id} job={job} featured />
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Regular Jobs */}
                  {regularJobs.length > 0 && (
                    <div>
                      {featuredJobs.length > 0 && (
                        <div className="flex items-center justify-between mb-6 mt-12">
                          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                            All Jobs
                          </h2>
                          <div className="h-px bg-gray-200 dark:bg-gray-700 flex-1 ml-6" />
                        </div>
                      )}
                      <div className="grid gap-4">
                        {regularJobs.map((job) => (
                          <JobCard key={job.id} job={job} />
                        ))}
                      </div>
                    </div>
                  )}

                  {/* No Jobs Found */}
                  {(!jobs || jobs.length === 0) && (
                    <div className="text-center py-12">
                      <div className="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Search className="w-12 h-12 text-gray-400" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                        No jobs found
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-6">
                        Try adjusting your search criteria or browse all available positions.
                      </p>
                      <Button onClick={() => setFilters({
                        search: '',
                        location: '',
                        employment_type: [],
                        experience_level: [],
                        salary_min: 0,
                        is_featured: false,
                        tags: []
                      })}>
                        Clear Filters
                      </Button>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Email Subscription */}
      <section className="bg-blue-50 dark:bg-blue-950/20 py-16">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
          <EmailSubscription />
        </div>
      </section>
    </div>
  )
}