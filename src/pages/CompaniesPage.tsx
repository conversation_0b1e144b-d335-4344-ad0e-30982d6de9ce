import React from 'react'
import { useState } from 'react'
import { motion } from 'framer-motion'
import { useQuery } from '@tanstack/react-query'
import { 
  Building,
  MapPin,
  Users,
  ExternalLink,
  Search,
  Filter,
  Globe,
  Star,
  Briefcase,
  Calendar,
  TrendingUp
} from 'lucide-react'
import { Link } from 'react-router-dom'
import { Button } from '../components/ui/button'
import { Badge } from '../components/ui/badge'
import { Card, CardContent } from '../components/ui/card'
import { Input } from '../components/ui/input'
import { Avatar, AvatarFallback, AvatarImage } from '../components/ui/avatar'
import { supabase, Company } from '../lib/supabase'
import JobsMap from '../components/Maps/JobsMap'

interface CompanyWithJobs extends Company {
  job_count: number
  featured_jobs_count: number
  latest_job_posted: string
}

export default function CompaniesPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedIndustry, setSelectedIndustry] = useState('')
  const [selectedSize, setSelectedSize] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'map'>('grid')

  // Fetch companies with job counts
  const { data: companies, isLoading } = useQuery({
    queryKey: ['companies', searchTerm, selectedIndustry, selectedSize],
    queryFn: async () => {
      let query = supabase
        .from('companies')
        .select(`
          *,
          jobs(count)
        `)
        .order('created_at', { ascending: false })

      if (searchTerm) {
        query = query.or(`name.ilike.%${searchTerm}%,industry.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`)
      }

      if (selectedIndustry) {
        query = query.eq('industry', selectedIndustry)
      }

      if (selectedSize) {
        query = query.eq('size_range', selectedSize)
      }

      const { data, error } = await query
      
      if (error) throw error
      
      // Transform data to include job counts
      return data.map(company => ({
        ...company,
        job_count: company.jobs?.[0]?.count || 0,
        featured_jobs_count: 0, // Will be calculated separately
        latest_job_posted: new Date().toISOString()
      })) as CompanyWithJobs[]
    },
  })

  // Sample map locations for demonstration
  const mapLocations = companies?.map(company => ({
    id: company.id,
    latitude: 40.7128 + (Math.random() - 0.5) * 20, // Random coordinates for demo
    longitude: -74.0060 + (Math.random() - 0.5) * 40,
    title: '',
    company: company.name,
    location: company.location || 'Global',
    type: 'company' as const,
    jobCount: company.job_count
  })) || []

  const industries = ['Technology', 'Software', 'Data Science', 'Design', 'Marketing', 'Sales', 'Finance', 'Healthcare']
  const sizeRanges = ['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+']

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <section className="bg-linear-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-900 dark:to-blue-950 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <Badge className="inline-flex items-center space-x-2 bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800 mb-6">
              <Building className="w-4 h-4" />
              <span>Verified Companies</span>
            </Badge>
            
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Discover Amazing
              <span className="block text-transparent bg-clip-text bg-linear-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400">
                Remote-First Companies
              </span>
            </h1>
            
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
              Explore innovative companies building the future of remote work. 
              Find your next opportunity with verified, remote-friendly employers.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Filters and Search */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  type="text"
                  placeholder="Search companies, industries, or technologies..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-12"
                />
              </div>
            </div>

            {/* Industry Filter */}
            <div className="lg:w-48">
              <select
                value={selectedIndustry}
                onChange={(e) => setSelectedIndustry(e.target.value)}
                className="w-full h-12 px-3 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Industries</option>
                {industries.map(industry => (
                  <option key={industry} value={industry}>{industry}</option>
                ))}
              </select>
            </div>

            {/* Size Filter */}
            <div className="lg:w-32">
              <select
                value={selectedSize}
                onChange={(e) => setSelectedSize(e.target.value)}
                className="w-full h-12 px-3 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Sizes</option>
                {sizeRanges.map(size => (
                  <option key={size} value={size}>{size}</option>
                ))}
              </select>
            </div>

            {/* View Toggle */}
            <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="flex-1"
              >
                <Building className="w-4 h-4 mr-2" />
                Grid
              </Button>
              <Button
                variant={viewMode === 'map' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('map')}
                className="flex-1"
              >
                <MapPin className="w-4 h-4 mr-2" />
                Map
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Content */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mr-3" />
            <span className="text-gray-600 dark:text-gray-400">Loading companies...</span>
          </div>
        ) : viewMode === 'map' ? (
          /* Map View */
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                Company Locations
              </h2>
              <Badge variant="secondary">
                {companies?.length || 0} companies
              </Badge>
            </div>
            <JobsMap 
              locations={mapLocations} 
              height="600px" 
              zoom={3}
              center={[40.7128, -74.0060]}
            />
          </div>
        ) : (
          /* Grid View */
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                {companies?.length || 0} Companies
              </h2>
              <div className="flex items-center space-x-4">
                <Badge variant="secondary" className="text-sm">
                  <TrendingUp className="w-4 h-4 mr-1" />
                  Growing Startups
                </Badge>
                <Badge variant="secondary" className="text-sm">
                  <Star className="w-4 h-4 mr-1" />
                  Verified Employers
                </Badge>
              </div>
            </div>

            {companies && companies.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {companies.map((company, index) => (
                  <motion.div
                    key={company.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <Card className="group hover:shadow-lg transition-all duration-200 border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600">
                      <CardContent className="p-6">
                        <div className="flex items-start space-x-4 mb-4">
                          <Avatar className="w-16 h-16 border-2 border-white dark:border-gray-800 shadow-sm">
                            <AvatarImage 
                              src={company.logo_url || '/images/modern_tech_company_office_workspace.jpg'} 
                              alt={company.name} 
                            />
                            <AvatarFallback className="bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 font-semibold text-lg">
                              {company.name.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                          
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">
                                {company.name}
                              </h3>
                              {company.is_verified && (
                                <Badge variant="secondary" className="text-xs bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300">
                                  Verified
                                </Badge>
                              )}
                            </div>
                            
                            {company.industry && (
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                {company.industry}
                              </p>
                            )}
                          </div>
                        </div>

                        {company.description && (
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-3">
                            {company.description}
                          </p>
                        )}

                        <div className="space-y-3 mb-4">
                          {company.location && (
                            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                              <MapPin className="w-4 h-4 mr-2" />
                              {company.location}
                            </div>
                          )}
                          
                          {company.size_range && (
                            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                              <Users className="w-4 h-4 mr-2" />
                              {company.size_range} employees
                            </div>
                          )}
                          
                          {company.founded_year && (
                            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                              <Calendar className="w-4 h-4 mr-2" />
                              Founded {company.founded_year}
                            </div>
                          )}
                          
                          {company.website_url && (
                            <div className="flex items-center text-sm">
                              <Globe className="w-4 h-4 mr-2 text-gray-400" />
                              <a 
                                href={company.website_url} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200"
                              >
                                Visit Website
                              </a>
                            </div>
                          )}
                        </div>

                        <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                          <div className="text-sm">
                            <span className="font-semibold text-blue-600 dark:text-blue-400">
                              {company.job_count}
                            </span>
                            <span className="text-gray-600 dark:text-gray-400 ml-1">
                              open {company.job_count === 1 ? 'position' : 'positions'}
                            </span>
                          </div>
                          
                          <Link to={`/company/${company.id}`}>
                            <Button 
                              size="sm" 
                              className="group-hover:bg-blue-600 group-hover:text-white transition-all duration-200"
                              variant="outline"
                            >
                              View Profile
                              <ExternalLink className="w-3 h-3 ml-1" />
                            </Button>
                          </Link>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Building className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  No companies found
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Try adjusting your search criteria or browse all companies.
                </p>
                <Button onClick={() => {
                  setSearchTerm('')
                  setSelectedIndustry('')
                  setSelectedSize('')
                }}>
                  Clear Filters
                </Button>
              </div>
            )}
          </div>
        )}
      </section>
    </div>
  )
}