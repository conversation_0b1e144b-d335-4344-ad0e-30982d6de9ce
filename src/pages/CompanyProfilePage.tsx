import React from 'react'
import { useParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { motion } from 'framer-motion'
import { 
  Building,
  MapPin,
  Users,
  Globe,
  Calendar,
  ExternalLink,
  Star,
  CheckCircle,
  Briefcase,
  TrendingUp,
  Eye,
  ArrowLeft,
  Mail,
  Phone
} from 'lucide-react'
import { Link } from 'react-router-dom'
import { Button } from '../components/ui/button'
import { Badge } from '../components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '../components/ui/avatar'
import { Separator } from '../components/ui/separator'
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '../components/ui/tabs'
import { supabase, Company } from '../lib/supabase'
import JobCard from '../components/Jobs/JobCard'
import JobsMap from '../components/Maps/JobsMap'

// Mock data for demonstration
const mockCompany = {
  id: '1',
  name: 'TechGlobal Inc.',
  logo_url: '/images/modern_tech_company_office_workspace.jpg',
  website_url: 'https://techglobal.com',
  description: 'TechGlobal Inc. is a leading technology company specializing in artificial intelligence and machine learning solutions. We are building the future of intelligent automation with a remote-first culture that values innovation, collaboration, and work-life balance. Our diverse team of 500+ professionals works from 30+ countries to deliver cutting-edge solutions to Fortune 500 companies worldwide.',
  location: 'San Francisco, CA (Remote-first)',
  size_range: '500-1000',
  industry: 'Artificial Intelligence',
  founded_year: 2018,
  is_verified: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

const mockJobs = [
  {
    id: '1',
    company_id: '1',
    title: 'Senior React Developer',
    slug: 'senior-react-developer',
    description: 'We are looking for an experienced React developer to join our frontend team...',
    requirements: 'Strong experience with React, TypeScript, and modern frontend tools',
    location: 'Remote',
    location_type: 'Remote',
    salary_min: 90,
    salary_max: 130,
    salary_currency: 'USD',
    employment_type: 'Full-time',
    experience_level: 'Senior Level',
    benefits: ['Health Insurance', 'Remote Work Stipend', 'Professional Development'],
    tags: ['React', 'TypeScript', 'JavaScript', 'Frontend'],
    is_featured: true,
    is_active: true,
    view_count: 245,
    application_count: 18,
    external_apply_url: null,
    how_to_apply: null,
    posted_by: null,
    posted_at: '2024-01-15T00:00:00Z',
    expires_at: null,
    created_at: '2024-01-15T00:00:00Z',
    updated_at: '2024-01-15T00:00:00Z',
    company: mockCompany
  },
  {
    id: '2',
    company_id: '1',
    title: 'DevOps Engineer',
    slug: 'devops-engineer',
    description: 'Join our infrastructure team to build scalable cloud solutions...',
    requirements: 'Experience with AWS, Docker, Kubernetes, and CI/CD pipelines',
    location: 'Remote',
    location_type: 'Remote',
    salary_min: 85,
    salary_max: 120,
    salary_currency: 'USD',
    employment_type: 'Full-time',
    experience_level: 'Mid Level',
    benefits: ['Health Insurance', 'Stock Options', 'Flexible PTO'],
    tags: ['AWS', 'Docker', 'Kubernetes', 'DevOps'],
    is_featured: false,
    is_active: true,
    view_count: 189,
    application_count: 12,
    external_apply_url: null,
    how_to_apply: null,
    posted_by: null,
    posted_at: '2024-01-10T00:00:00Z',
    expires_at: null,
    created_at: '2024-01-10T00:00:00Z',
    updated_at: '2024-01-10T00:00:00Z',
    company: mockCompany
  }
]

const companyStats = {
  totalJobs: 24,
  activeJobs: 18,
  totalViews: 5420,
  totalApplications: 234,
  averageSalary: '$95k - $125k',
  responseRate: '95%',
  hireRate: '15%'
}

const teamMembers = [
  {
    name: 'Sarah Chen',
    role: 'Head of Engineering',
    avatar: '/images/business_team_remote_collaboration_hybrid_meeting_modern_workplace.jpg'
  },
  {
    name: 'Marcus Rodriguez',
    role: 'VP of Product',
    avatar: '/images/business_team_hybrid_remote_collaboration_meeting.jpg'
  },
  {
    name: 'Emily Watson',
    role: 'Director of People',
    avatar: '/images/business_team_remote_collaboration_video_meeting_modern_office.jpg'
  }
]

const benefits = [
  'Competitive salary and equity',
  'Comprehensive health insurance',
  'Flexible PTO and sabbaticals',
  '$2,000 remote work stipend',
  'Professional development budget',
  'Mental health support',
  'Parental leave program',
  'Annual team retreats'
]

const values = [
  {
    title: 'Remote-First Culture',
    description: 'We believe the best work happens when people have the freedom to work from anywhere.'
  },
  {
    title: 'Innovation & Learning',
    description: 'We encourage experimentation and provide resources for continuous learning and growth.'
  },
  {
    title: 'Diversity & Inclusion',
    description: 'We are committed to building a diverse team that reflects the global community we serve.'
  },
  {
    title: 'Work-Life Balance',
    description: 'We respect boundaries and believe that rest and recreation fuel better work.'
  }
]

export default function CompanyProfilePage() {
  const { id } = useParams<{ id: string }>()

  // For demo purposes, we'll use mock data
  // In a real app, you would fetch company data from Supabase
  const company = mockCompany
  const jobs = mockJobs

  // Mock map locations
  const mapLocations = [
    {
      id: '1',
      latitude: 37.7749,
      longitude: -122.4194,
      title: 'Headquarters',
      company: company.name,
      location: 'San Francisco, CA',
      type: 'company' as const,
      jobCount: companyStats.activeJobs
    },
    ...jobs.map(job => ({
      id: job.id,
      latitude: 37.7749 + (Math.random() - 0.5) * 0.1,
      longitude: -122.4194 + (Math.random() - 0.5) * 0.1,
      title: job.title,
      company: job.company?.name || '',
      location: job.location || 'Remote',
      isFeatured: job.is_featured,
      type: 'job' as const
    }))
  ]

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <div className="mb-6">
          <Link to="/companies">
            <Button variant="ghost" className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Companies
            </Button>
          </Link>
        </div>

        {/* Company Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <Card className="overflow-hidden">
            {/* Cover Image */}
            <div className="h-48 bg-linear-to-r from-blue-600 to-indigo-600 relative">
              <div className="absolute inset-0 bg-black/20" />
            </div>
            
            <CardContent className="p-8 -mt-16 relative">
              <div className="flex flex-col lg:flex-row items-start lg:items-end space-y-6 lg:space-y-0 lg:space-x-6">
                {/* Company Logo */}
                <Avatar className="w-32 h-32 border-4 border-white dark:border-gray-800 shadow-lg bg-white">
                  <AvatarImage src={company.logo_url} alt={company.name} />
                  <AvatarFallback className="bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 font-bold text-4xl">
                    {company.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                
                {/* Company Info */}
                <div className="flex-1">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div>
                      <div className="flex items-center space-x-3 mb-2">
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                          {company.name}
                        </h1>
                        {company.is_verified && (
                          <Badge className="bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800">
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Verified
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex flex-wrap items-center gap-4 text-gray-600 dark:text-gray-400 mb-4">
                        <div className="flex items-center">
                          <Building className="w-4 h-4 mr-2" />
                          {company.industry}
                        </div>
                        <div className="flex items-center">
                          <MapPin className="w-4 h-4 mr-2" />
                          {company.location}
                        </div>
                        <div className="flex items-center">
                          <Users className="w-4 h-4 mr-2" />
                          {company.size_range} employees
                        </div>
                        <div className="flex items-center">
                          <Calendar className="w-4 h-4 mr-2" />
                          Founded {company.founded_year}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      {company.website_url && (
                        <a href={company.website_url} target="_blank" rel="noopener noreferrer">
                          <Button variant="outline">
                            <Globe className="w-4 h-4 mr-2" />
                            Visit Website
                          </Button>
                        </a>
                      )}
                      <Button className="bg-linear-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white">
                        <Briefcase className="w-4 h-4 mr-2" />
                        View Jobs
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4 mb-8"
        >
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {companyStats.activeJobs}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Active Jobs</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {companyStats.totalViews.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Total Views</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {companyStats.totalApplications}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Applications</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-lg font-bold text-orange-600 dark:text-orange-400">
                {companyStats.averageSalary}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Avg. Salary</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-teal-600 dark:text-teal-400">
                {companyStats.responseRate}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Response Rate</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-pink-600 dark:text-pink-400">
                {companyStats.hireRate}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Hire Rate</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">
                4.9
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Rating</div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-8">
            {/* Tabs */}
            <Tabs value="overview" onValueChange={() => {}} className="space-y-6">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="jobs">Jobs ({jobs.length})</TabsTrigger>
                <TabsTrigger value="culture">Culture</TabsTrigger>
                <TabsTrigger value="locations">Locations</TabsTrigger>
              </TabsList>

              {/* Overview Tab */}
              <TabsContent value="overview" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>About {company.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                      {company.description}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>What We Offer</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {benefits.map((benefit, index) => (
                        <div key={index} className="flex items-center">
                          <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mr-3 shrink-0" />
                          <span className="text-gray-600 dark:text-gray-400">{benefit}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Our Values</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {values.map((value, index) => (
                        <div key={index} className="space-y-2">
                          <h4 className="font-semibold text-gray-900 dark:text-white">
                            {value.title}
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {value.description}
                          </p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Jobs Tab */}
              <TabsContent value="jobs" className="space-y-6">
                <div className="space-y-4">
                  {jobs.map((job) => (
                    <JobCard key={job.id} job={job} />
                  ))}
                </div>
                
                {jobs.length === 0 && (
                  <Card>
                    <CardContent className="p-12 text-center">
                      <Briefcase className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        No open positions
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        Check back later for new opportunities or follow the company for updates.
                      </p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              {/* Culture Tab */}
              <TabsContent value="culture" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Meet the Team</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {teamMembers.map((member, index) => (
                        <div key={index} className="text-center">
                          <img
                            src={member.avatar}
                            alt={member.name}
                            className="w-24 h-24 rounded-full mx-auto mb-4 object-cover"
                          />
                          <h4 className="font-semibold text-gray-900 dark:text-white mb-1">
                            {member.name}
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {member.role}
                          </p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Company Culture</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <img
                        src="/images/business_team_remote_collaboration_hybrid_meeting_modern_workplace.jpg"
                        alt="Team collaboration"
                        className="w-full h-64 object-cover rounded-lg"
                      />
                      <p className="text-gray-600 dark:text-gray-400">
                        Our culture is built on trust, transparency, and collaboration. We believe that the best work 
                        happens when people feel empowered to bring their authentic selves to work and have the 
                        flexibility to work in ways that suit them best.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Locations Tab */}
              <TabsContent value="locations" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Our Locations & Remote-First Approach</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <JobsMap 
                      locations={mapLocations}
                      height="400px"
                      center={[37.7749, -122.4194]}
                      zoom={10}
                    />
                    <div className="mt-6 space-y-4">
                      <div className="flex items-start space-x-3">
                        <Building className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-1" />
                        <div>
                          <h4 className="font-semibold text-gray-900 dark:text-white">Headquarters</h4>
                          <p className="text-gray-600 dark:text-gray-400">San Francisco, CA</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <Globe className="w-5 h-5 text-green-600 dark:text-green-400 mt-1" />
                        <div>
                          <h4 className="font-semibold text-gray-900 dark:text-white">Remote Team</h4>
                          <p className="text-gray-600 dark:text-gray-400">
                            Our team works from 30+ countries around the world, with flexible hours 
                            and regular virtual team building events.
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Right Sidebar */}
          <div className="space-y-6">
            {/* Quick Apply */}
            <Card className="sticky top-24">
              <CardHeader>
                <CardTitle>Interested in joining?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Browse our open positions or get in touch with our recruiting team.
                </p>
                
                <div className="space-y-3">
                  <Button className="w-full bg-linear-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white">
                    View Open Positions
                  </Button>
                  
                  <Button variant="outline" className="w-full">
                    <Mail className="w-4 h-4 mr-2" />
                    Contact Recruiting
                  </Button>
                </div>
                
                <Separator />
                
                <div className="text-center text-sm text-gray-500 dark:text-gray-400">
                  <div className="flex items-center justify-center space-x-4">
                    <div className="flex items-center">
                      <Eye className="w-4 h-4 mr-1" />
                      <span>{companyStats.totalViews.toLocaleString()} profile views</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Company Info */}
            <Card>
              <CardHeader>
                <CardTitle>Company Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Industry</span>
                  <span className="font-medium text-gray-900 dark:text-white">{company.industry}</span>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Company Size</span>
                  <span className="font-medium text-gray-900 dark:text-white">{company.size_range} employees</span>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Founded</span>
                  <span className="font-medium text-gray-900 dark:text-white">{company.founded_year}</span>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Headquarters</span>
                  <span className="font-medium text-gray-900 dark:text-white">{company.location}</span>
                </div>
                
                <Separator />
                
                <div className="space-y-3">
                  {company.website_url && (
                    <a href={company.website_url} target="_blank" rel="noopener noreferrer">
                      <Button variant="outline" className="w-full justify-start">
                        <Globe className="w-4 h-4 mr-2" />
                        Company Website
                        <ExternalLink className="w-3 h-3 ml-auto" />
                      </Button>
                    </a>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}