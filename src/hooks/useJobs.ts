import { useQuery, useMutation } from '@tanstack/react-query'
import { supabase } from '../lib/supabase'

export function useJobs() {
  return useQuery({
    queryKey: ['jobs'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('jobs')
        .select(`
          *,
          company:companies(*),
          job_locations(*),
          featured_jobs(*)
        `)
        .eq('is_active', true)
        .order('posted_at', { ascending: false })
        .limit(50)
      
      if (error) throw error
      return data
    },
  })
}

export function useJobStats() {
  return useMutation({
    mutationFn: async ({ action, jobId, userId }: { action: string, jobId: string, userId?: string }) => {
      const { data, error } = await supabase
        .from('job_stats')
        .insert({
          job_id: jobId,
          user_id: userId,
          action: action,
          created_at: new Date().toISOString()
        })
      
      if (error) throw error
      return data
    }
  })
}