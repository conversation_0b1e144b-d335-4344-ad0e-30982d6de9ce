import React from 'react'
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { AuthProvider } from './contexts/AuthContext'
import { ThemeProvider } from './contexts/ThemeContext'
import Header from './components/Layout/Header'
import Footer from './components/Layout/Footer'
import HomePage from './pages/HomePage'
import JobDetailPage from './pages/JobDetailPage'
import CompaniesPage from './pages/CompaniesPage'
import CompanyProfilePage from './pages/CompanyProfilePage'
import PostJobPage from './pages/PostJobPage'
import EmployerDashboard from './pages/EmployerDashboard'
import PricingPage from './pages/PricingPage'
import LoginPage from './pages/LoginPage'
import RegisterPage from './pages/RegisterPage'
import ProfilePage from './pages/ProfilePage'
import AuthCallback from './components/Auth/AuthCallback'
import './index.css'

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
})

// Layout wrapper for main app pages
function LayoutWrapper({ children }: { children: React.ReactNode }) {
  return (
    <>
      <Header />
      <main className="pt-20">
        {children}
      </main>
      <Footer />
    </>
  )
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <AuthProvider>
          <Router>
            <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
              <Routes>
                {/* Auth routes (no header/footer) */}
                <Route path="/auth/login" element={<LoginPage />} />
                <Route path="/auth/register" element={<RegisterPage />} />
                <Route path="/auth/callback" element={<AuthCallback />} />
                
                {/* Main app routes (with header/footer) */}
                <Route path="/" element={
                  <LayoutWrapper>
                    <HomePage />
                  </LayoutWrapper>
                } />
                <Route path="/jobs/:slug" element={
                  <LayoutWrapper>
                    <JobDetailPage />
                  </LayoutWrapper>
                } />
                <Route path="/companies" element={
                  <LayoutWrapper>
                    <CompaniesPage />
                  </LayoutWrapper>
                } />
                <Route path="/company/:id" element={
                  <LayoutWrapper>
                    <CompanyProfilePage />
                  </LayoutWrapper>
                } />
                <Route path="/post-job" element={
                  <LayoutWrapper>
                    <PostJobPage />
                  </LayoutWrapper>
                } />
                <Route path="/pricing" element={
                  <LayoutWrapper>
                    <PricingPage />
                  </LayoutWrapper>
                } />
                <Route path="/dashboard" element={
                  <LayoutWrapper>
                    <EmployerDashboard />
                  </LayoutWrapper>
                } />
                <Route path="/profile" element={
                  <LayoutWrapper>
                    <ProfilePage />
                  </LayoutWrapper>
                } />
              </Routes>
            </div>
          </Router>
        </AuthProvider>
      </ThemeProvider>
    </QueryClientProvider>
  )
}

export default App