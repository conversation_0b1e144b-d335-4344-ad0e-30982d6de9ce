{"name": "react_repo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "yes | pnpm install && vite", "build": "yes | pnpm install && rm -rf node_modules/.vite-temp && tsc -b && vite build", "build:prod": "yes | pnpm install && rm -rf node_modules/.vite-temp && tsc -b && BUILD_MODE=prod vite build", "lint": "yes | pnpm install && eslint .", "lint:fix": "yes | pnpm install && eslint . --fix", "preview": "yes | pnpm install && vite preview", "analyze": "npx vite-bundle-analyzer"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@react-google-maps/api": "^2.20.7", "@supabase/supabase-js": "^2.55.0", "@tanstack/react-query": "^5.85.3", "@types/google.maps": "^3.58.1", "@types/leaflet": "^1.9.20", "date-fns": "4.1.0", "framer-motion": "^12.23.12", "leaflet": "^1.9.4", "lucide-react": "0.540.0", "next-themes": "^0.4.4", "react": "19.1.1", "react-dom": "19.1.1", "react-leaflet": "5.0.0", "react-router-dom": "7.8.1", "recharts": "3.1.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.15.0", "@tailwindcss/postcss": "^4.1.12", "@types/node": "24.3.0", "@types/react": "19.1.10", "@types/react-dom": "19.1.7", "@types/react-router-dom": "^5", "@vitejs/plugin-react": "5.0.1", "autoprefixer": "10.4.21", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "16.3.0", "postcss": "8.5.6", "tailwindcss": "4.1.12", "typescript": "5.9.2", "typescript-eslint": "^8.15.0", "vite": "7.1.3", "vite-plugin-source-info": "^1.0.0"}, "packageManager": "pnpm@10.15.0+sha512.486ebc259d3e999a4e8691ce03b5cac4a71cbeca39372a9b762cb500cfdf0873e2cb16abe3d951b1ee2cf012503f027b98b6584e4df22524e0c7450d9ec7aa7b"}